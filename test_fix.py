#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的代码，验证NoneType错误是否已解决
"""

import sys
import os
import traceback
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_none_chunk_handling():
    """测试None chunk的处理"""
    print("测试1: None chunk处理...")
    
    # 模拟None chunk的情况
    chunk = None
    
    try:
        # 测试安全的属性访问
        safe_filename = chunk.get('filename', '未知文件') if chunk and isinstance(chunk, dict) else '未知文件'
        safe_chunk_index = chunk.get('chunk_index', 0) if chunk and isinstance(chunk, dict) else 0
        safe_content = chunk.get('content', '无内容') if chunk and isinstance(chunk, dict) else '无内容'
        
        print(f"  [OK] 安全访问成功: filename={safe_filename}, chunk_index={safe_chunk_index}, content={safe_content}")
        return True

    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_empty_dict_chunk_handling():
    """测试空字典chunk的处理"""
    print("测试2: 空字典chunk处理...")
    
    # 模拟空字典chunk的情况
    chunk = {}
    
    try:
        # 测试安全的属性访问
        safe_filename = chunk.get('filename', '未知文件') if chunk and isinstance(chunk, dict) else '未知文件'
        safe_chunk_index = chunk.get('chunk_index', 0) if chunk and isinstance(chunk, dict) else 0
        safe_content = chunk.get('content', '无内容') if chunk and isinstance(chunk, dict) else '无内容'
        
        print(f"  [OK] 安全访问成功: filename={safe_filename}, chunk_index={safe_chunk_index}, content={safe_content}")
        return True

    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_normal_chunk_handling():
    """测试正常chunk的处理"""
    print("测试3: 正常chunk处理...")
    
    # 模拟正常chunk的情况
    chunk = {
        'filename': '测试文件.docx',
        'chunk_index': 1,
        'content': '这是测试内容',
        'char_count': 100
    }
    
    try:
        # 测试安全的属性访问
        safe_filename = chunk.get('filename', '未知文件') if chunk and isinstance(chunk, dict) else '未知文件'
        safe_chunk_index = chunk.get('chunk_index', 0) if chunk and isinstance(chunk, dict) else 0
        safe_content = chunk.get('content', '无内容') if chunk and isinstance(chunk, dict) else '无内容'
        
        print(f"  [OK] 安全访问成功: filename={safe_filename}, chunk_index={safe_chunk_index}, content={safe_content}")
        return True

    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_exception_handling_with_none_chunk():
    """测试异常处理中的None chunk"""
    print("测试4: 异常处理中的None chunk...")
    
    chunk = None
    
    try:
        # 模拟异常处理逻辑
        safe_filename = '未知文件'
        safe_chunk_index = 0
        safe_content = '无内容'
        
        if chunk is not None and isinstance(chunk, dict):
            safe_filename = chunk.get('filename', '未知文件')
            safe_chunk_index = chunk.get('chunk_index', 0)
            safe_content = chunk.get('content', '无内容')
        
        # 模拟失败记录创建
        if chunk is not None and isinstance(chunk, dict):
            failed_chunk = {
                **chunk,
                'failure_reason': 'processing_exception',
                'error_message': 'test error'
            }
        else:
            # 如果chunk为None，创建一个基本的失败记录
            failed_chunk = {
                'filename': safe_filename,
                'chunk_index': safe_chunk_index,
                'content': safe_content,
                'failure_reason': 'processing_exception_null_chunk',
                'error_message': 'test error'
            }
        
        print(f"  [OK] 异常处理成功: {failed_chunk}")
        return True

    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的代码...")
    print("=" * 50)
    
    tests = [
        test_none_chunk_handling,
        test_empty_dict_chunk_handling,
        test_normal_chunk_handling,
        test_exception_handling_with_none_chunk
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"  [ERROR] 测试异常: {str(e)}")
            traceback.print_exc()
            print()

    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("[SUCCESS] 所有测试通过！修复成功。")
        return True
    else:
        print("[FAIL] 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

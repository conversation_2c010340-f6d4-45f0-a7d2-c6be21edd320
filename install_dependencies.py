#!/usr/bin/env python3
"""
依赖安装脚本
分析并安装项目所需的所有依赖包
"""

import subprocess
import sys
import os
import platform
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info("✓ Python版本符合要求")
    return True

def run_command(cmd, description="", check=True, index_url=None):
    """运行命令并处理错误"""
    if index_url:
        if "pip install" in cmd:
            cmd += f" --index-url {index_url}"
    try:
        logger.info(f"执行: {description or cmd}")
        result = subprocess.run(cmd, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            logger.info(result.stdout.strip())
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {cmd}")
        if e.stderr:
            logger.error(f"错误信息: {e.stderr.strip()}")
        return False, e.stderr

def check_pip():
    """检查pip是否可用"""
    success, _ = run_command(f"{sys.executable} -m pip --version", "检查pip", check=False)
    if not success:
        logger.error("pip不可用，请先安装pip")
        return False
    
    logger.info("✓ pip可用")
    return True

def upgrade_pip():
    """升级pip到最新版本"""
    logger.info("升级pip...")
    # 使用阿里源升级pip
    success, _ = run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip", index_url="https://mirrors.aliyun.com/pypi/simple/")
    return success

def install_core_dependencies():
    """安装核心依赖"""
    logger.info("=== 安装核心依赖 ===")
    
    core_packages = [
        "openai",
        "python-docx", 
        "pandas",
        "tiktoken",
        "requests",
        "pdfplumber"
    ]
    
    failed_packages = []
    
    for package in core_packages:
        logger.info(f"安装 {package}...")
        success, _ = run_command(f"{sys.executable} -m pip install {package}", check=False, index_url="https://mirrors.aliyun.com/pypi/simple/")
        if not success:
            failed_packages.append(package)
            logger.error(f"✗ {package} 安装失败")
        else:
            logger.info(f"✓ {package} 安装成功")
    
    return len(failed_packages) == 0, failed_packages

def install_textract():
    """安装textract及其依赖"""
   
    return True
       
    


def install_gui_dependencies():
    """安装GUI依赖（可选）"""
    logger.info("=== 安装GUI依赖（可选）===")
    
    gui_packages = [
        "customtkinter>=5.0.0",
        "Pillow>=8.0.0"
    ]
    
    failed_packages = []
    
    for package in gui_packages:
        logger.info(f"安装 {package}...")
        success, _ = run_command(f"{sys.executable} -m pip install {package}", check=False, index_url="https://mirrors.aliyun.com/pypi/simple/")
        if not success:
            failed_packages.append(package)
            logger.warning(f"⚠ {package} 安装失败（GUI功能可能受限）")
        else:
            logger.info(f"✓ {package} 安装成功")
    
    return len(failed_packages) == 0, failed_packages

def install_from_requirements():
    """从requirements.txt安装依赖"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        logger.error("requirements.txt文件不存在")
        return False
    
    logger.info("=== 从requirements.txt安装依赖 ===")
    success, _ = run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装requirements.txt", index_url="https://mirrors.aliyun.com/pypi/simple/")
    return success

def verify_installation():
    """验证安装结果"""
    logger.info("=== 验证安装结果 ===")
    
    test_imports = [
        ("openai", "OpenAI API客户端"),
        ("docx", "python-docx"),
        ("pandas", "pandas"),
        ("tiktoken", "tiktoken"),
        ("requests", "requests"),
        ("pdfplumber", "pdfplumber"),
    ]
    
    optional_imports = [
    
    ]
    
    core_success = True
    for module, name in test_imports:
        try:
            __import__(module)
            logger.info(f"✓ {name} 导入成功")
        except ImportError:
            logger.error(f"✗ {name} 导入失败")
            core_success = False
    
    optional_success = 0
    for module, name in optional_imports:
        try:
            __import__(module)
            logger.info(f"✓ {name} 导入成功")
            optional_success += 1
        except ImportError:
            logger.warning(f"⚠ {name} 导入失败（可选功能）")
    
    logger.info(f"\n=== 安装验证结果 ===")
    logger.info(f"核心依赖: {'✓ 全部成功' if core_success else '✗ 部分失败'}")
    logger.info(f"可选依赖: {optional_success}/{len(optional_imports)} 成功")
    
    return core_success

def main():
    """主安装函数"""
    logger.info("=== 项目依赖安装脚本 ===")
    logger.info(f"操作系统: {platform.system()} {platform.release()}")
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查pip
    if not check_pip():
        return 1
    
    # 升级pip
    if not upgrade_pip():
        logger.warning("pip升级失败，继续使用当前版本")
    
    # 分步安装
    # 安装核心依赖
    core_success, failed_core = install_core_dependencies()
    if not core_success:
        logger.error(f"核心依赖安装失败: {failed_core}")
        return 1
    
    # 安装textract
    textract_success = install_textract()
    if not textract_success:
        logger.warning("textract安装失败，DOC文件处理功能可能受限")
    
    # 询问是否安装GUI依赖
    install_gui = input("是否安装GUI依赖？(y/n): ").strip().lower()
    if install_gui == 'y':
        gui_success, failed_gui = install_gui_dependencies()
        if not gui_success:
            logger.warning(f"GUI依赖安装失败: {failed_gui}")
    
    # 验证安装
    if verify_installation():
        logger.info("🎉 依赖安装完成！")
        logger.info("现在可以运行:")
        logger.info("  python main.py --help")
        logger.info("  python gui_app.py")
        return 0
    else:
        logger.error("❌ 依赖安装验证失败")
        logger.info("请检查错误信息并手动解决")
        return 1

if __name__ == "__main__":
    sys.exit(main())

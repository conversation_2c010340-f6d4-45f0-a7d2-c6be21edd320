import os
import sys
import logging
from typing import List, Dict, Any
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import csv
import json
import threading
import queue
import time

from config import Config
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor
from utils.helpers import (
    setup_logging, validate_input_directory, create_argument_parser,
    print_banner, print_progress_bar, validate_api_config,
    estimate_processing_time, summarize_results, scan_directory_structure
)

logger = logging.getLogger(__name__)

class MainApplication:
    def __init__(self, root):
        self.root = root
        self.root.title("文件处理工具")
        self.root.geometry("1400x800")  # 增大窗口以容纳右侧显示区域

        # 初始化线程相关变量
        self.processing_thread = None
        self.question_queue = queue.Queue()
        self.is_processing = False
        self.has_generated_questions = False # 新增：用于跟踪是否已成功生成题目

        # 创建主框架和标签页
        self.create_notebook()

        # 初始化配置并自动加载
        self.config = self.load_config()

        # 初始化分块合并变量，默认启用且不在GUI显示
        self.enable_chunk_merging_var = tk.BooleanVar(value=True)

        self.load_config_to_gui()

        # 设置状态
        self.status_var.set("就绪")

        # 启动队列监听
        self.check_question_queue()

    def create_notebook(self):
        """创建标签页界面"""
        # 创建Notebook控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建主要操作标签页
        self.main_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.main_frame, text="主要操作")

        # 创建设置标签页
        self.settings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.settings_frame, text="设置")

        # 在主要操作标签页中创建控件
        self.create_main_tab_widgets()

        # 在设置标签页中创建控件
        self.create_settings_tab_widgets()

    def create_main_tab_widgets(self):
        """创建主要操作标签页的控件"""
        # 创建左右分栏
        self.create_main_paned_window()

        # 左侧控制面板
        self.create_left_control_panel()

        # 右侧题目显示区域
        self.create_right_question_panel()

    def create_main_paned_window(self):
        """创建主要操作标签页的分栏窗口"""
        # 创建PanedWindow用于左右分栏
        self.main_paned = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧框架（控制面板）
        self.left_frame = ttk.Frame(self.main_paned, padding="5")
        self.main_paned.add(self.left_frame, weight=1)

        # 右侧框架（题目显示）
        self.right_frame = ttk.Frame(self.main_paned, padding="5")
        self.main_paned.add(self.right_frame, weight=1)

    def create_left_control_panel(self):
        """创建左侧控制面板"""
        # 文件选择部分
        self.create_file_selection_frame()

        # 输出配置部分
        self.create_output_config_frame()

        # 执行按钮
        self.create_execute_button()

        # 状态显示
        self.create_status_frame()

    def create_right_question_panel(self):
        """创建右侧题目显示面板"""
        # 题目显示框架
        question_frame = ttk.LabelFrame(self.right_frame, text="生成的题目", padding="5")
        question_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动文本框显示题目
        self.question_display = scrolledtext.ScrolledText(
            question_frame,
            wrap=tk.WORD,
            width=50,
            height=30,
            font=("Consolas", 10)
        )
        self.question_display.pack(fill=tk.BOTH, expand=True)

        # 添加清空按钮
        button_frame = ttk.Frame(question_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(button_frame, text="清空显示", command=self.clear_question_display).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="保存题目", command=self.save_displayed_questions).pack(side=tk.LEFT, padx=(5, 0))

        # 进度显示
        self.progress_frame = ttk.Frame(question_frame)
        self.progress_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(self.progress_frame, text="处理进度:").pack(side=tk.LEFT)
        self.progress_var = tk.StringVar(value="0/0")
        ttk.Label(self.progress_frame, textvariable=self.progress_var).pack(side=tk.LEFT, padx=(5, 0))

        # 进度条
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

    def create_settings_tab_widgets(self):
        """创建设置标签页的控件"""
        # API配置部分
        self.create_api_config_frame()

        # 文本处理配置部分
        self.create_processing_config_frame()

        # 题型数量配置部分
        self.create_question_types_config_frame()

        # 保存设置按钮
        self.create_save_settings_button()

    def create_file_selection_frame(self):
        file_frame = ttk.LabelFrame(self.left_frame, text="文件选择", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        # 文件选择按钮
        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=0, padx=5)
        ttk.Button(file_frame, text="选择文件夹", command=self.select_folder).grid(row=0, column=1, padx=5)

        # 递归选项
        self.recursive_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(file_frame, text="递归读取子文件夹", variable=self.recursive_var).grid(row=0, column=2, padx=5)

        # 显示选择的路径
        self.file_path_var = tk.StringVar()
        ttk.Label(file_frame, textvariable=self.file_path_var).grid(row=1, column=0, columnspan=3, sticky=tk.W)

        # 文件类型选择
        file_types_frame = ttk.LabelFrame(file_frame, text="选择处理的文件类型", padding="3")
        file_types_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        # 创建文件类型复选框变量
        self.enable_pdf_var = tk.BooleanVar(value=True)
        self.enable_docx_var = tk.BooleanVar(value=True)
        self.enable_md_var = tk.BooleanVar(value=True)
        self.enable_txt_var = tk.BooleanVar(value=True)

        # 创建复选框
        ttk.Checkbutton(file_types_frame, text="PDF", variable=self.enable_pdf_var).grid(row=0, column=0, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="DOCX", variable=self.enable_docx_var).grid(row=0, column=1, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="MD", variable=self.enable_md_var).grid(row=0, column=2, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="TXT", variable=self.enable_txt_var).grid(row=1, column=0, padx=5, sticky=tk.W)

        # 全选/全不选按钮
        button_frame = ttk.Frame(file_types_frame)
        button_frame.grid(row=1, column=2, padx=5, sticky=tk.W)
        ttk.Button(button_frame, text="全选", command=self.select_all_file_types, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="全不选", command=self.deselect_all_file_types, width=8).pack(side=tk.LEFT, padx=2)

        # 目录结构预览按钮
        ttk.Button(file_frame, text="预览目录结构", command=self.preview_directory).grid(row=3, column=0, columnspan=3, pady=5)

    def create_output_config_frame(self):
        """创建输出配置框架（在主界面中）"""
        config_frame = ttk.LabelFrame(self.left_frame, text="输出配置", padding="5")
        config_frame.pack(fill=tk.X, pady=5)

        # CSV文件位置
        ttk.Label(config_frame, text="CSV文件位置:").grid(row=0, column=0, sticky=tk.W)
        self.csv_path_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_path_var).grid(row=0, column=1, sticky=(tk.W, tk.E))
        ttk.Button(config_frame, text="浏览", command=self.select_csv_path).grid(row=0, column=2, padx=5)

        # CSV文件名
        ttk.Label(config_frame, text="CSV文件名:").grid(row=1, column=0, sticky=tk.W)
        self.csv_name_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_name_var).grid(row=1, column=1, sticky=(tk.W, tk.E))

        # CSV模板
        ttk.Label(config_frame, text="CSV模板:").grid(row=2, column=0, sticky=tk.W)
        self.csv_template_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_template_var).grid(row=2, column=1, sticky=(tk.W, tk.E))

    def create_api_config_frame(self):
        """创建API配置框架（在设置标签页中）"""
        api_frame = ttk.LabelFrame(self.settings_frame, text="API配置", padding="5")
        api_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # API基础URL
        ttk.Label(api_frame, text="API基础URL:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.api_base_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_base_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # API密钥
        ttk.Label(api_frame, text="API密钥:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.api_key_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 模型名称
        ttk.Label(api_frame, text="模型名称:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.model_name_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.model_name_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 温度设置
        ttk.Label(api_frame, text="温度:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.temperature_var = tk.DoubleVar(value=0.7)
        temp_frame = ttk.Frame(api_frame)
        temp_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Scale(temp_frame, from_=0.0, to=1.0, variable=self.temperature_var,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT)
        ttk.Label(temp_frame, textvariable=self.temperature_var).pack(side=tk.LEFT, padx=5)

        # 最大token数
        ttk.Label(api_frame, text="最大Token数:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.max_tokens_var = tk.IntVar(value=2000)
        ttk.Entry(api_frame, textvariable=self.max_tokens_var, width=40).grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        api_frame.columnconfigure(1, weight=1)

    def create_processing_config_frame(self):
        """创建文本处理配置框架（在设置标签页中）"""
        proc_frame = ttk.LabelFrame(self.settings_frame, text="文本处理配置", padding="5")
        proc_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 文本块大小
        ttk.Label(proc_frame, text="文本块大小 (tokens):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.max_chunk_size_var = tk.IntVar(value=2000)
        chunk_size_frame = ttk.Frame(proc_frame)
        chunk_size_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        chunk_scale = ttk.Scale(chunk_size_frame, from_=500, to=8000, variable=self.max_chunk_size_var,
                               orient=tk.HORIZONTAL, length=200)
        chunk_scale.pack(side=tk.LEFT)
        chunk_scale.configure(command=lambda v: self.max_chunk_size_var.set(int(float(v))))
        ttk.Label(chunk_size_frame, textvariable=self.max_chunk_size_var).pack(side=tk.LEFT, padx=5)
        # 添加文本块大小说明
        ttk.Label(proc_frame, text="每个文本块的最大token数，影响处理速度和质量。", font=("Arial", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)

        # 题目基础字数设置
        ttk.Label(proc_frame, text="题目基础字数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.question_base_chars_var = tk.IntVar(value=2000)
        base_chars_frame = ttk.Frame(proc_frame)
        base_chars_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        base_chars_scale = ttk.Scale(base_chars_frame, from_=500, to=5000, variable=self.question_base_chars_var,
                                    orient=tk.HORIZONTAL, length=200)
        base_chars_scale.pack(side=tk.LEFT)
        base_chars_scale.configure(command=lambda v: self.question_base_chars_var.set(int(float(v))))
        ttk.Label(base_chars_frame, textvariable=self.question_base_chars_var).pack(side=tk.LEFT, padx=5)
        # 添加题目基础字数说明
        ttk.Label(proc_frame, text="生成一组题目的基础字数。", font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)

        # 文档分割选项
        ttk.Label(proc_frame, text="文档分割:").grid(row=2, column=0, sticky=tk.W, pady=5)
        splitting_options_frame = ttk.Frame(proc_frame) # 新建一个frame来容纳复选框和说明
        splitting_options_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5) # columnspan=2 以便容纳更多内容

        self.disable_splitting_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(splitting_options_frame, text="不进行文档分割",
                       variable=self.disable_splitting_var).pack(side=tk.LEFT)
        ttk.Label(splitting_options_frame, text="勾选后将对整个文档生成题目，忽略分块设置。", font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=5)

        # 新分块逻辑选项
        ttk.Label(proc_frame, text="分块逻辑:").grid(row=3, column=0, sticky=tk.W, pady=5)
        new_logic_options_frame = ttk.Frame(proc_frame) # 新建一个frame来容纳复选框和说明
        new_logic_options_frame.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5) # columnspan=2 以便容纳更多内容

        self.use_new_splitting_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(new_logic_options_frame, text="使用新分块逻辑",
                       variable=self.use_new_splitting_var).pack(side=tk.LEFT)
        ttk.Label(new_logic_options_frame, text="采用更智能的分段方式，通常生成无重叠的文本块，并根据字数进行调整。", font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=5)

        # 预设配置按钮
        preset_frame = ttk.Frame(proc_frame)
        preset_frame.grid(row=4, column=0, columnspan=3, pady=10)

        ttk.Button(preset_frame, text="快速配置", command=self.apply_fast_preset).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="平衡配置", command=self.apply_balanced_preset).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="精细配置", command=self.apply_detailed_preset).pack(side=tk.LEFT, padx=5)

        proc_frame.columnconfigure(1, weight=1)

    def create_question_types_config_frame(self):
        """创建题型数量配置框架（在设置标签页中）"""
        types_frame = ttk.LabelFrame(self.settings_frame, text="题型数量配置", padding="5")
        types_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 题型配置说明 (移动到顶部右侧)
        info_text = "说明：设置每个文本块生成各种题型的数量，总题目数量为各题型数量之和。新出题逻辑将根据文档字数和基础字数自动调整题目数量。基础字数是指生成一道题所需的最少文本字数。基础题数是指在满足基础字数条件下，每个文本块或文档段落预期生成的题目数量。"
        ttk.Label(types_frame, text=info_text, font=("Arial", 8), foreground="gray", wraplength=300).grid(row=0, column=2, rowspan=7, sticky=tk.NW, padx=5, pady=5)

        # 单选题数量
        ttk.Label(types_frame, text="单选题数量:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.single_choice_var = tk.IntVar(value=4)
        single_frame = ttk.Frame(types_frame)
        single_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        single_scale = ttk.Scale(single_frame, from_=0, to=10, variable=self.single_choice_var,
                                orient=tk.HORIZONTAL, length=150)
        single_scale.pack(side=tk.LEFT)
        single_scale.configure(command=lambda v: self.single_choice_var.set(int(float(v))))
        ttk.Label(single_frame, textvariable=self.single_choice_var).pack(side=tk.LEFT, padx=5)

        # 多选题数量
        ttk.Label(types_frame, text="多选题数量:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.multiple_choice_var = tk.IntVar(value=2)
        multiple_frame = ttk.Frame(types_frame)
        multiple_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        multiple_scale = ttk.Scale(multiple_frame, from_=0, to=10, variable=self.multiple_choice_var,
                                  orient=tk.HORIZONTAL, length=150)
        multiple_scale.pack(side=tk.LEFT)
        multiple_scale.configure(command=lambda v: self.multiple_choice_var.set(int(float(v))))
        ttk.Label(multiple_frame, textvariable=self.multiple_choice_var).pack(side=tk.LEFT, padx=5)

        # 填空题数量
        ttk.Label(types_frame, text="填空题数量:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.fill_blank_var = tk.IntVar(value=2)
        fill_frame = ttk.Frame(types_frame)
        fill_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        fill_scale = ttk.Scale(fill_frame, from_=0, to=10, variable=self.fill_blank_var,
                              orient=tk.HORIZONTAL, length=150)
        fill_scale.pack(side=tk.LEFT)
        fill_scale.configure(command=lambda v: self.fill_blank_var.set(int(float(v))))
        ttk.Label(fill_frame, textvariable=self.fill_blank_var).pack(side=tk.LEFT, padx=5)

        # 简答题数量
        ttk.Label(types_frame, text="简答题数量:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.short_answer_var = tk.IntVar(value=1)
        short_frame = ttk.Frame(types_frame)
        short_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        short_scale = ttk.Scale(short_frame, from_=0, to=10, variable=self.short_answer_var,
                               orient=tk.HORIZONTAL, length=150)
        short_scale.pack(side=tk.LEFT)
        short_scale.configure(command=lambda v: self.short_answer_var.set(int(float(v))))
        ttk.Label(short_frame, textvariable=self.short_answer_var).pack(side=tk.LEFT, padx=5)

        # 判断题数量
        ttk.Label(types_frame, text="判断题数量:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.true_false_var = tk.IntVar(value=2)
        judge_frame = ttk.Frame(types_frame)
        judge_frame.grid(row=5, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        judge_scale = ttk.Scale(judge_frame, from_=0, to=10, variable=self.true_false_var,
                               orient=tk.HORIZONTAL, length=150)
        judge_scale.pack(side=tk.LEFT)
        judge_scale.configure(command=lambda v: self.true_false_var.set(int(float(v))))
        ttk.Label(judge_frame, textvariable=self.true_false_var).pack(side=tk.LEFT, padx=5)

        # 排序题数量
        ttk.Label(types_frame, text="排序题数量:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.sorting_var = tk.IntVar(value=0) # 默认为0，因为排序题比较复杂
        sorting_frame = ttk.Frame(types_frame)
        sorting_frame.grid(row=6, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        sorting_scale = ttk.Scale(sorting_frame, from_=0, to=10, variable=self.sorting_var,
                                 orient=tk.HORIZONTAL, length=150)
        sorting_scale.pack(side=tk.LEFT)
        sorting_scale.configure(command=lambda v: self.sorting_var.set(int(float(v))))
        ttk.Label(sorting_frame, textvariable=self.sorting_var).pack(side=tk.LEFT, padx=5)

        types_frame.columnconfigure(1, weight=1)

    def create_save_settings_button(self):
        """创建保存设置按钮（在设置标签页中）"""
        ttk.Button(self.settings_frame, text="保存设置", command=self.save_config_and_notify).grid(row=3, column=0, columnspan=2, pady=10)

    def create_execute_button(self):
        button_frame = ttk.Frame(self.left_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.execute_btn = ttk.Button(button_frame, text="开始处理", command=self.execute)
        self.execute_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_btn = ttk.Button(button_frame, text="停止处理", command=self.stop_processing, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT)

    def create_status_frame(self):
        status_frame = ttk.LabelFrame(self.left_frame, text="状态", padding="5")
        status_frame.pack(fill=tk.X, pady=5)

        self.status_var = tk.StringVar()
        ttk.Label(status_frame, textvariable=self.status_var).pack(anchor=tk.W)

    def clear_question_display(self):
        """清空题目显示"""
        self.question_display.delete(1.0, tk.END)

    def save_displayed_questions(self):
        """保存显示的题目到文件"""
        content = self.question_display.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("警告", "没有题目可保存")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.* подойдёт")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"题目已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def add_question_to_display(self, question_data):
        """添加题目到显示区域"""
        if not question_data:
            logger.debug("add_question_to_display: question_data为None或空，不处理。")
            return

        # 格式化题目显示
        display_text = self.format_question_for_display(question_data)

        # 在文本框末尾添加题目
        self.question_display.insert(tk.END, display_text + "\n" + "="*50 + "\n")

        # 自动滚动到底部
        self.question_display.see(tk.END)

    def format_question_for_display(self, question_data):
        """格式化题目用于显示"""
        if not question_data or 'questions' not in question_data:
            logger.debug(f"format_question_for_display: question_data格式不正确或无questions键: {question_data}")
            return "题目生成失败或格式不正确"

        formatted_text = ""
        for i, question in enumerate(question_data['questions'], 1):
            if not isinstance(question, dict):
                logger.warning(f"format_question_for_display: 检测到非字典格式题目或None值，跳过显示: {question}")
                continue

            question_type = question.get('type', '未知类型')
            question_text = question.get('question', '无题目内容')

            formatted_text += f"题目 {i} ({question_type}):\n"
            formatted_text += f"  {question_text}\n"

            # 根据题型显示选项或答案
            if question_type in ['单选题', '多选题']:
                options = question.get('options', [])
                if not isinstance(options, list):
                    logger.warning(f"format_question_for_display: 题目选项非列表类型，跳过: {options}")
                    options = [] # 确保是列表
                for j, option in enumerate(options):
                    formatted_text += f"  {chr(65+j)}. {option}\n"
                answer = question.get('answer', '无答案')
                formatted_text += f"  答案: {answer}\n"
            elif question_type == '填空题':
                answer = question.get('answer', '无答案')
                formatted_text += f"  答案: {answer}\n"
            elif question_type == '简答题':
                answer = question.get('answer', '无答案')
                formatted_text += f"  参考答案: {answer}\n"
            elif question_type == '判断题':
                answer = question.get('answer', '无答案')
                formatted_text += f"  答案: {answer}\n"

            formatted_text += "\n"

        return formatted_text.strip()

    def check_question_queue(self):
        """检查题目队列并更新显示"""
        try:
            while True:
                message = self.question_queue.get_nowait()
                message_type = message.get('type')

                if message_type == 'question':
                    # 新题目
                    self.add_question_to_display(message.get('data'))
                    self.has_generated_questions = True # 有题目生成，设置标志
                elif message_type == 'progress':
                    # 进度更新
                    progress_data = message.get('data')
                    current = progress_data.get('current', 0)
                    total = progress_data.get('total', 0)
                    filename = progress_data.get('filename', '')

                    self.progress_var.set(f"{current}/{total}")
                    if total > 0:
                        self.progress_bar['maximum'] = total
                        self.progress_bar['value'] = current

                    self.status_var.set(f"正在处理: {filename}")
                elif message_type == 'status':
                    # 状态更新
                    self.status_var.set(message.get('data', ''))
                elif message_type == 'complete':
                    # 处理完成
                    self.processing_complete()
                elif message_type == 'failure':
                    # 生成失败信息
                    failure_data = message.get('data', {})
                    filename = failure_data.get('filename', '未知文件')
                    chunk_index = failure_data.get('chunk_index', 0)
                    reason = failure_data.get('reason', '未知原因')
                    chunk_content = failure_data.get('chunk_content', '无内容') # 提取文本内容
                    char_count = len(chunk_content) if chunk_content != '无内容' else 0

                    # 如果已经有题目生成，只在后台输出简化信息，不显示在GUI，不进入错误记录流程
                    if self.has_generated_questions:
                        # 后台输出：只包含文件名、第几个分块、字数、错误原因
                        print(f"⚠️  文本块未生成题目 - 文件: {filename}, 分块: {chunk_index+1}, 字数: {char_count}, 原因: {reason}")
                    else:
                        # 还没有题目生成，在GUI显示详细信息并进入错误记录流程
                        failure_text = f"❌ 生成失败 - 文件: {filename} (块 {chunk_index})\n"
                        failure_text += f"   原因: {reason}\n"
                        failure_text += f"   失败文本段落:\n"
                        failure_text += f"   ----------------------------------------\n"
                        failure_text += f"   {chunk_content}\n"
                        failure_text += f"   ----------------------------------------\n"
                        failure_text += f"   提示：此失败已记录在 'err_docs' 文件夹中。\n"
                        failure_text += "="*50 + "\n"

                        # 在题目显示区域显示失败信息
                        self.question_display.insert(tk.END, failure_text)
                        self.question_display.see(tk.END)
                elif message_type == 'merge_info':
                    # 分块合并信息
                    merge_data = message.get('data', {})
                    filename = merge_data.get('filename', '未知文件')
                    original_index = merge_data.get('original_index', 0)
                    merged_to_index = merge_data.get('merged_to_index', 0)
                    char_count = merge_data.get('char_count', 0)

                    merge_text = f"🔗 分块合并: {filename}\n"
                    merge_text += f"   索引 {original_index} 合并到 {merged_to_index}\n"
                    merge_text += f"   合并后字符数: {char_count}\n"
                    merge_text += "="*50 + "\n"

                    # 在题目显示区域显示合并信息
                    self.question_display.insert(tk.END, merge_text)
                    self.question_display.see(tk.END)
                elif message_type == 'incremental_save':
                    # 增量保存信息
                    save_data = message.get('data', {})
                    questions_saved = save_data.get('questions_saved', 0)
                    total_saved = save_data.get('total_saved', 0)
                    file_path = save_data.get('file_path', '')
                    backup_created = save_data.get('backup_created', False)

                    save_text = f"💾 增量保存: {questions_saved} 道题目 (总计: {total_saved})\n"
                    if backup_created:
                        save_text += "📦 自动备份已创建\n"
                    save_text += f"📁 文件: {os.path.basename(file_path) if file_path else '未知'}\n"
                    save_text += "="*50 + "\n"

                    # 在题目显示区域显示保存信息
                    self.question_display.insert(tk.END, save_text)
                    self.question_display.see(tk.END)
                elif message_type == 'final_save':
                    # 最终保存信息
                    save_data = message.get('data', {})
                    questions_saved = save_data.get('questions_saved', 0)
                    total_saved = save_data.get('total_saved', 0)
                    file_path = save_data.get('file_path', '')

                    if questions_saved > 0:
                        save_text = f"✅ 最终保存: {questions_saved} 道题目 (总计: {total_saved})\n"
                        save_text += f"📁 文件: {os.path.basename(file_path) if file_path else '未知'}\n"
                        save_text += "="*50 + "\n"

                        # 在题目显示区域显示保存信息
                        self.question_display.insert(tk.END, save_text)
                        self.question_display.see(tk.END)
                elif message_type == 'error':
                    # 错误信息
                    error_msg = message.get('data', '未知错误')
                    self.status_var.set(f"错误: {error_msg}")
                    messagebox.showerror("处理错误", error_msg)
                    self.processing_complete()

        except queue.Empty:
            pass

        # 继续检查队列
        self.root.after(100, self.check_question_queue)

    def processing_complete(self):
        """处理完成后的清理工作"""
        self.is_processing = False
        self.execute_btn.config(state=tk.NORMAL, text="开始处理")
        self.stop_btn.config(state=tk.DISABLED)
        self.status_var.set("处理完成")

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.is_alive():
            self.is_processing = False
            self.status_var.set("正在停止...")
            # 注意：这里只是设置标志，实际的线程停止需要在处理循环中检查

    def select_file(self):
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)

    def select_folder(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.file_path_var.set(folder_path)

    def select_csv_path(self):
        csv_path = filedialog.askdirectory()
        if csv_path:
            self.csv_path_var.set(csv_path)

    def preview_directory(self):
        """预览目录结构"""
        directory = self.file_path_var.get()
        if not directory:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        if not os.path.isdir(directory):
            messagebox.showwarning("警告", "选择的路径不是文件夹")
            return

        try:
            recursive = self.recursive_var.get()
            # 获取当前启用的文件类型
            enabled_formats = []
            if self.enable_pdf_var.get():
                enabled_formats.append('.pdf')
            if self.enable_docx_var.get():
                enabled_formats.append('.docx')
            if self.enable_md_var.get():
                enabled_formats.append('.md')
            if self.enable_txt_var.get():
                enabled_formats.append('.txt')

            structure_info = scan_directory_structure(directory, recursive, enabled_formats)

            if 'error' in structure_info:
                messagebox.showerror("错误", structure_info['error'])
                return

            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("目录结构预览")
            preview_window.geometry("600x400")

            # 创建文本框显示结构信息
            text_frame = ttk.Frame(preview_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 构建显示内容
            content = f"目录结构预览 (递归: {recursive})\n"
            content += "=" * 50 + "\n\n"
            content += f"总文件数: {structure_info['total_files']}\n"
            content += f"支持的文档数: {structure_info['supported_files']}\n"
            content += f"文件类型分布: {structure_info['file_types']}\n\n"

            content += "各目录文件分布:\n"
            content += "-" * 30 + "\n"

            for directory_path, files in structure_info['files_by_directory'].items():
                if files:  # 只显示有支持文件的目录
                    display_path = directory_path if directory_path else "根目录"
                    content += f"\n📁 {display_path} ({len(files)} 个文件):\n"
                    for file in files:
                        content += f"  📄 {file}\n"

            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"预览目录结构时发生错误：{str(e)}")

    def select_all_file_types(self):
        """全选文件类型"""
        self.enable_pdf_var.set(True)
        self.enable_docx_var.set(True)
        self.enable_md_var.set(True)
        self.enable_txt_var.set(True)

    def deselect_all_file_types(self):
        """全不选文件类型"""
        self.enable_pdf_var.set(False)
        self.enable_docx_var.set(False)
        self.enable_md_var.set(False)
        self.enable_txt_var.set(False)

    def apply_fast_preset(self):
        """应用快速配置预设"""
        self.max_chunk_size_var.set(1500)
        self.question_base_chars_var.set(1500)
        # 快速配置：较少题目
        self.single_choice_var.set(1)
        self.multiple_choice_var.set(1)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(0)
        self.true_false_var.set(1)
        self.sorting_var.set(0)
        messagebox.showinfo("配置应用", "已应用快速配置：\n• 文本块大小: 1500 字符\n• 题目基础字数: 1500\n• 题型: 单选1+多选1+填空1+判断1")

    def apply_balanced_preset(self):
        """应用平衡配置预设"""
        self.max_chunk_size_var.set(2000)
        self.question_base_chars_var.set(2000)
        # 平衡配置：中等题目数量
        self.single_choice_var.set(2)
        self.multiple_choice_var.set(1)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(1)
        self.true_false_var.set(1)
        self.sorting_var.set(0)
        messagebox.showinfo("配置应用", "已应用平衡配置：\n• 文本块大小: 2000 字符\n• 题目基础字数: 2000\n• 题型: 单选2+多选1+填空1+简答1+判断1")

    def apply_detailed_preset(self):
        """应用精细配置预设"""
        self.max_chunk_size_var.set(5000)
        self.question_base_chars_var.set(1300)
        # 精细配置：较多题目
        self.single_choice_var.set(4)
        self.multiple_choice_var.set(2)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(1)
        self.true_false_var.set(2)
        self.sorting_var.set(0)
        messagebox.showinfo("配置应用", "已应用精细配置：\n• 文本块大小: 5000 字符\n• 题目基础字数: 1300\n• 题型: 单选4+多选2+填空1+简答1+判断2+排序0")

    def test_connection(self):
        try:
            # 先保存当前配置
            self.save_config()
            # 加载配置
            config = Config.load_config()
            # 创建处理器实例
            processor = DocumentToQuizProcessor(config)
            # 测试连接
            if processor.test_api_connection():
                messagebox.showinfo("成功", "API连接测试成功！")
            else:
                messagebox.showerror("错误", "API连接测试失败！")
        except Exception as e:
            messagebox.showerror("错误", f"测试连接时发生错误：{str(e)}")

    def load_config(self):
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 设置LLM配置的默认值
                if 'api_base' not in config:
                    config['api_base'] = ''
                if 'api_key' not in config:
                    config['api_key'] = ''
                if 'model_name' not in config:
                    config['model_name'] = 'gpt-3.5-turbo'
                if 'temperature' not in config:
                    config['temperature'] = 0.7
                if 'max_tokens' not in config:
                    config['max_tokens'] = 2000
                # 设置文本处理配置的默认值
                if 'max_chunk_size' not in config:
                    config['max_chunk_size'] = 2000
                # 设置题型数量配置的默认值
                if 'single_choice_count' not in config:
                    config['single_choice_count'] = 4
                if 'multiple_choice_count' not in config:
                    config['multiple_choice_count'] = 2
                if 'fill_blank_count' not in config:
                    config['fill_blank_count'] = 2
                if 'short_answer_count' not in config:
                    config['short_answer_count'] = 1
                if 'true_false_count' not in config:
                    config['true_false_count'] = 2
                if 'sorting_count' not in config:
                    config['sorting_count'] = 0
                # 设置文档分割配置的默认值
                if 'disable_document_splitting' not in config:
                    config['disable_document_splitting'] = False
                # 设置分块合并配置的默认值
                if 'enable_chunk_merging' not in config:
                    config['enable_chunk_merging'] = True # 默认启用
                return config
        except FileNotFoundError:
            return {
                'csv_path': '',
                'csv_name': 'output.csv',
                'csv_template': 'template.csv',
                'api_base': '',
                'api_key': '',
                'model_name': 'gpt-3.5-turbo',
                'temperature': 0.7,
                'max_tokens': 2000,
                'max_chunk_size': 2000,
                'single_choice_count': 4,
                'multiple_choice_count': 2,
                'fill_blank_count': 2,
                'short_answer_count': 1,
                'true_false_count': 2,
                'sorting_count': 0,
                'disable_document_splitting': False,
                'enable_chunk_merging': True # 默认启用
            }

    def save_config(self):
        config = {
            'csv_path': self.csv_path_var.get(),
            'csv_name': self.csv_name_var.get(),
            'csv_template': self.csv_template_var.get(),
            'api_base': self.api_base_var.get(),
            'api_key': self.api_key_var.get(),
            'model_name': self.model_name_var.get(),
            'temperature': self.temperature_var.get(),
            'max_tokens': self.max_tokens_var.get(),
            'max_chunk_size': self.max_chunk_size_var.get(),
            'question_base_chars': self.question_base_chars_var.get(),
            'single_choice_count': self.single_choice_var.get(),
            'multiple_choice_count': self.multiple_choice_var.get(),
            'fill_blank_count': self.fill_blank_var.get(),
            'short_answer_count': self.short_answer_var.get(),
            'true_false_count': self.true_false_var.get(),
            'sorting_count': self.sorting_var.get(),
            'enable_pdf': self.enable_pdf_var.get(),
            'enable_docx': self.enable_docx_var.get(),
            'enable_md': self.enable_md_var.get(),
            'enable_txt': self.enable_txt_var.get(),
            'disable_document_splitting': self.disable_splitting_var.get(),
            'enable_chunk_merging': self.enable_chunk_merging_var.get(), # 新增：保存分块合并配置
            'use_new_splitting_logic': self.use_new_splitting_var.get()
        }
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

    def load_config_to_gui(self):
        """将配置加载到GUI控件中"""
        # 加载CSV配置
        self.csv_path_var.set(self.config.get('csv_path', ''))
        self.csv_name_var.set(self.config.get('csv_name', 'output.csv'))
        self.csv_template_var.set(self.config.get('csv_template', 'template.csv'))

        # 加载LLM配置
        self.api_base_var.set(self.config.get('api_base', ''))
        self.api_key_var.set(self.config.get('api_key', ''))
        self.model_name_var.set(self.config.get('model_name', 'gpt-3.5-turbo'))
        self.temperature_var.set(self.config.get('temperature', 0.7))
        self.max_tokens_var.set(self.config.get('max_tokens', 2000))

        # 加载文本处理配置
        self.max_chunk_size_var.set(self.config.get('max_chunk_size', 2000))
        self.question_base_chars_var.set(self.config.get('question_base_chars', 2000))

        # 加载题型数量配置
        self.single_choice_var.set(self.config.get('single_choice_count', 4))
        self.multiple_choice_var.set(self.config.get('multiple_choice_count', 2))
        self.fill_blank_var.set(self.config.get('fill_blank_count', 2))
        self.short_answer_var.set(self.config.get('short_answer_count', 1))
        self.true_false_var.set(self.config.get('true_false_count', 2))
        self.sorting_var.set(self.config.get('sorting_count', 0))

        # 加载文件类型选择配置
        self.enable_pdf_var.set(self.config.get('enable_pdf', True))
        self.enable_docx_var.set(self.config.get('enable_docx', True))
        self.enable_md_var.set(self.config.get('enable_md', True))
        self.enable_txt_var.set(self.config.get('enable_txt', True))

        # 加载文档分割配置
        self.disable_splitting_var.set(self.config.get('disable_document_splitting', False))
        # 加载分块合并配置
        self.enable_chunk_merging_var.set(self.config.get('enable_chunk_merging', True)) # 默认启用

        # 加载新分块逻辑配置
        self.use_new_splitting_var.set(self.config.get('use_new_splitting_logic', True))

    def save_config_and_notify(self):
        """保存设置并显示通知"""
        try:
            self.save_config()
            messagebox.showinfo("成功", "设置已保存")
            self.status_var.set("设置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败：{str(e)}")
            self.status_var.set("保存设置失败")

    def execute(self):
        """执行文档处理"""
        if self.is_processing:
            messagebox.showwarning("警告", "正在处理中，请等待完成或停止当前处理")
            return

        try:
            # 检查输入路径
            input_path = self.file_path_var.get()
            if not input_path:
                messagebox.showwarning("警告", "请先选择文件或文件夹")
                return

            # 自动保存当前配置
            self.save_config()

            # 清空题目显示
            self.clear_question_display()

            # 设置处理状态
            self.is_processing = True
            self.execute_btn.config(state=tk.DISABLED, text="处理中...")
            self.stop_btn.config(state=tk.NORMAL)
            self.status_var.set("准备开始处理...")

            # 启动处理线程
            self.processing_thread = threading.Thread(
                target=self.process_documents_thread,
                args=(input_path,),
                daemon=True
            )
            self.processing_thread.start()

        except Exception as e:
            logger.error(f"启动处理失败: {str(e)}")
            messagebox.showerror("错误", f"启动处理失败: {str(e)}")
            self.processing_complete()

    def process_documents_thread(self, input_path):
        """在线程中处理文档"""
        try:
            # 创建自定义配置对象
            config = Config()

            # 从GUI获取配置
            config.API_BASE_URL = self.api_base_var.get()
            config.API_KEY = self.api_key_var.get()
            config.MODEL_NAME = self.model_name_var.get()
            config.TEMPERATURE = self.temperature_var.get()
            config.MAX_TOKENS = self.max_tokens_var.get()
            config.MAX_CHUNK_SIZE = self.max_chunk_size_var.get()

            # 设置题型数量配置
            config.SINGLE_CHOICE_COUNT = self.single_choice_var.get()
            config.MULTIPLE_CHOICE_COUNT = self.multiple_choice_var.get()
            config.FILL_BLANK_COUNT = self.fill_blank_var.get()
            config.SHORT_ANSWER_COUNT = self.short_answer_var.get()
            config.TRUE_FALSE_COUNT = self.true_false_var.get()
            config.SORTING_COUNT = self.sorting_var.get()

            # 设置文件类型选择配置
            config.ENABLE_PDF = self.enable_pdf_var.get()
            config.ENABLE_DOCX = self.enable_docx_var.get()
            config.ENABLE_MD = self.enable_md_var.get()
            config.ENABLE_TXT = self.enable_txt_var.get()

            # 设置文档分割配置
            config.DISABLE_DOCUMENT_SPLITTING = self.disable_splitting_var.get()
            # 设置分块合并配置
            config.ENABLE_CHUNK_MERGING = self.enable_chunk_merging_var.get() # 新增：从GUI获取分块合并配置

            # 设置新分块逻辑配置
            config.USE_NEW_SPLITTING_LOGIC = self.use_new_splitting_var.get()
            config.BASE_CHAR_THRESHOLD = self.question_base_chars_var.get()  # 使用题目基础字数作为阈值

            # 计算总题目数量
            config.QUESTIONS_PER_CHUNK = (config.SINGLE_CHOICE_COUNT +
                                        config.MULTIPLE_CHOICE_COUNT +
                                        config.FILL_BLANK_COUNT +
                                        config.SHORT_ANSWER_COUNT +
                                        config.TRUE_FALSE_COUNT +
                                        config.SORTING_COUNT)

            # 设置输出目录
            if self.csv_path_var.get():
                config.OUTPUT_DIR = self.csv_path_var.get()
            if self.csv_name_var.get():
                config.CSV_FILENAME = self.csv_name_var.get()

            # 创建处理器
            processor = DocumentToQuizProcessor(config)

            # 执行处理（带进度回调）
            recursive = self.recursive_var.get()
            result = self.process_with_progress_callback(processor, input_path, recursive)

            # 根据 result 的状态发送不同的消息
            if result.get('status') == 'stopped':
                self.question_queue.put({
                    'type': 'status',
                    'data': f"处理已停止。已生成题目数: {result.get('total_questions', 0)}"
                })
            elif 'error' in result:
                self.question_queue.put({
                    'type': 'error',
                    'data': result['error']
                })
            else:
                success_msg = f"处理完成！\n"
                success_msg += f"生成题目数: {result.get('total_questions', 0)}\n"
                success_msg += f"处理文件数: {result.get('processed_files_count', 0)}\n"
                success_msg += f"成功率: {result.get('success_rate', 0):.1f}%"

                if 'csv_file' in result:
                    success_msg += f"\n输出文件: {result['csv_file']}"

                self.question_queue.put({
                    'type': 'status',
                    'data': success_msg
                })
                self.question_queue.put({'type': 'complete'})

        except Exception as e:
            logger.error(f"处理线程失败: {str(e)}")
            self.question_queue.put({
                'type': 'error',
                'data': f"处理失败: {str(e)}"
            })
        finally:
            # 无论处理成功、失败或停止，都确保调用 processing_complete
            self.processing_complete()

    def process_with_progress_callback(self, processor, input_path, recursive):
        """带进度回调的处理方法"""
        try:
            # 发送状态更新
            self.question_queue.put({
                'type': 'status',
                'data': '正在读取文档...'
            })

            # 1. 读取文档
            documents = processor.doc_reader.batch_read_documents(input_path, recursive)
            if not documents:
                return {'error': '没有找到可处理的文档'}

            # 2. 分割文本
            self.question_queue.put({
                'type': 'status',
                'data': '正在分割文本...'
            })
            chunks = processor.text_splitter.batch_split_documents(documents)
            if not chunks:
                return {'error': '文本分割失败'}

            # 3. 处理文本块并生成题目
            self.question_queue.put({
                'type': 'status',
                'data': '开始生成题目...'
            })

            all_questions = []
            failed_chunks = []
            successful_chunk_indices = [] # 新增：用于记录成功生成题目的文本块索引

            total_chunks = len(chunks)
            i = 0

            while i < len(chunks):
                # 检查是否已请求停止处理
                if not self.is_processing:
                    logger.info("用户请求停止处理。")
                    self.question_queue.put({'type': 'status', 'data': '处理已停止。'})
                    return {'status': 'stopped', 'total_questions': len(all_questions), 'processed_files_count': i}

                chunk = chunks[i]

                if chunk is None:
                    filename_for_log = '未知文件' # 在 chunk 为 None 时使用默认值
                    logger.warning(f"检测到空文本块，跳过处理 - 文件: {filename_for_log}, 索引: {i}")
                    self.question_queue.put({
                        'type': 'failure',
                        'data': {
                            'filename': filename_for_log,
                            'chunk_index': i,
                            'reason': '空文本块',
                            'chunk_content': 'None'
                        }
                    })
                    i += 1
                    continue

                try:
                    # 再次检查chunk是否为None（防御性编程）
                    if chunk is None or not isinstance(chunk, dict):
                        logger.warning(f"在try块中检测到无效的chunk，跳过处理 - 索引: {i}")
                        i += 1
                        continue

                    # 发送进度更新
                    self.question_queue.put({
                        'type': 'progress',
                        'data': {
                            'current': i + 1,
                            'total': total_chunks,
                            'filename': chunk.get('filename', '未知文件')
                        }
                    })

                    # 根据字符数计算题目数量
                    char_count = len(chunk.get('content', '').strip())
                    question_counts = processor.question_calculator.calculate_question_counts(char_count)

                    # 生成题目
                    quiz_data = processor.llm_client.generate_quiz(
                        content=chunk.get('content', ''),
                        source_filename=chunk.get('filename', '未知文档'),
                        question_counts=question_counts # 传递计算出的题目数量
                    )

                    # **修改后的逻辑：优先处理quiz_data为None的情况**
                    if quiz_data is None:
                        # 内容不足，检查是否禁用了分块或合并
                        if processor.config.DISABLE_DOCUMENT_SPLITTING:
                            safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                            logger.warning(f"处理文本块失败: {safe_filename} - 原因: 文档分割已禁用，内容不足以生成题目")
                            failed_chunk = {
                                **chunk,
                                'failure_reason': 'insufficient_content_no_splitting'
                            }
                            failed_chunks.append(failed_chunk)
                            self.question_queue.put({
                                'type': 'failure',
                                'data': {
                                    'filename': chunk.get('filename', '未知文件'),
                                    'chunk_index': chunk.get('chunk_index', 0),
                                    'reason': '文档分割已禁用，内容不足以生成题目',
                                    'chunk_content': chunk.get('content', '无内容')
                                }
                            })
                            i += 1
                        elif not processor.config.ENABLE_CHUNK_MERGING:
                            safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                            logger.warning(f"处理文本块失败: {safe_filename} - 原因: 分块合并已禁用，内容不足以生成题目")
                            failed_chunk = {
                                **chunk,
                                'failure_reason': 'insufficient_content_no_merging'
                            }
                            failed_chunks.append(failed_chunk)
                            self.question_queue.put({
                                'type': 'failure',
                                'data': {
                                    'filename': chunk.get('filename', '未知文件'),
                                    'chunk_index': chunk.get('chunk_index', 0),
                                    'reason': '分块合并已禁用，内容不足以生成题目',
                                    'chunk_content': chunk.get('content', '无内容')
                                }
                            })
                            i += 1
                        else:
                            # 尝试合并分块
                            merged_result = processor._try_merge_chunks_for_generation(chunks, i)
                            if merged_result['success']:
                                merged_chunk = merged_result['merged_chunk']
                                next_index = merged_result['next_index']
                                self.question_queue.put({
                                    'type': 'merge_info',
                                    'data': {
                                        'filename': merged_chunk['filename'],
                                        'original_index': i,
                                        'merged_to_index': next_index - 1,
                                        'char_count': merged_chunk['char_count']
                                    }
                                })

                                merged_quiz_data = processor.llm_client.generate_quiz(
                                    content=merged_chunk['content'],
                                    source_filename=merged_chunk.get('filename', '未知文档'),
                                    question_counts=processor.question_calculator.calculate_question_counts(merged_chunk.get('char_count', 0)) # 传递合并后的题目数量
                                )

                                if merged_quiz_data and 'questions' in merged_quiz_data:
                                    chunk_questions = []
                                    for question in merged_quiz_data['questions']:
                                        if not isinstance(question, dict):
                                            logger.warning(f"process_with_progress_callback: 合并后检测到非字典格式题目或None值，跳过处理: {question}")
                                            continue
                                        question['source_file'] = merged_chunk['filename']
                                        question['chunk_index'] = merged_chunk['chunk_index']
                                        question['merged_from'] = merged_chunk.get('merged_from', [])
                                        chunk_questions.append(question)
                                        all_questions.append(question)
                                    save_result = processor.incremental_saver.add_questions(chunk_questions)
                                    
                                    # 记录成功生成题目的文本块索引
                                    if merged_quiz_data['questions']:
                                        for original_idx in merged_chunk.get('merged_from', []):
                                            successful_chunk_indices.append(original_idx)
                                        # 如果是单个分块成功，也加入它的索引
                                        if not merged_chunk.get('merged_from') and 'chunk_index' in merged_chunk:
                                            successful_chunk_indices.append(merged_chunk['chunk_index'])

                                    self.question_queue.put({
                                        'type': 'question',
                                        'data': merged_quiz_data
                                    })
                                    if save_result.get('saved'):
                                        self.question_queue.put({
                                            'type': 'incremental_save',
                                            'data': {
                                                'questions_saved': save_result.get('questions_saved', 0),
                                                'total_saved': save_result.get('total_saved', 0),
                                                'file_path': save_result.get('file_path', ''),
                                                'backup_created': save_result.get('backup_info', {}).get('created', False)
                                            }
                                        })
                                    elif 'error' in save_result:
                                        self.question_queue.put({
                                            'type': 'incremental_save',
                                            'data': {
                                                'questions_saved': save_result.get('questions_saved', 0),
                                                'total_saved': save_result.get('total_saved', 0),
                                                'file_path': save_result.get('file_path', ''),
                                                'backup_created': save_result.get('backup_info', {}).get('created', False),
                                                'error': save_result.get('error', '未知保存错误')
                                            }
                                        })
                                    logger.info(f"合并分块成功生成 {len(merged_quiz_data['questions'])} 道题目")
                                else:
                                    logger.warning(f"处理文本块失败: {merged_chunk['filename']} - 原因: 合并后仍无法生成题目")
                                    failed_chunks.append({
                                        **merged_chunk,
                                        'failure_reason': 'insufficient_content_after_merge'
                                    })
                                    self.question_queue.put({
                                        'type': 'failure', # 从 failure_log 改为 failure
                                        'data': {
                                            'filename': merged_chunk['filename'],
                                            'chunk_index': merged_chunk['chunk_index'],
                                            'reason': '合并后仍无法生成题目',
                                            'chunk_content': merged_chunk.get('content', '无内容') # 添加文本内容
                                        }
                                    })
                                i = next_index
                            else:
                                safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                                logger.warning(f"处理文本块失败: {safe_filename} - 原因: 内容不足或生成失败")
                                failed_chunks.append({
                                    **chunk,
                                    'failure_reason': 'insufficient_content_or_generation_failed'
                                })
                                self.question_queue.put({
                                    'type': 'failure', # 从 failure_log 改为 failure
                                    'data': {
                                        'filename': chunk.get('filename', '未知文件'),
                                        'chunk_index': chunk.get('chunk_index', 0),
                                        'reason': '内容不足或生成失败',
                                        'chunk_content': chunk.get('content', '无内容') # 添加文本内容
                                    }
                                })
                                i += 1

                    elif 'questions' in quiz_data: # quiz_data不是None，且有questions键
                        # 为每个题目添加来源信息
                        chunk_questions = []
                        for question in quiz_data['questions']:
                            if not isinstance(question, dict):
                                logger.warning(f"process_with_progress_callback: 检测到非字典格式题目或None值，跳过处理: {question}")
                                continue
                            question['source_file'] = chunk.get('filename', '未知文件')
                            question['chunk_index'] = chunk.get('chunk_index', 0)
                            chunk_questions.append(question)
                            all_questions.append(question)

                        # 记录成功生成题目的文本块索引
                        if quiz_data['questions']:
                            chunk_index = chunk.get('chunk_index', 0) if chunk else 0
                            successful_chunk_indices.append(chunk_index)

                        # 增量保存题目
                        save_result = processor.incremental_saver.add_questions(chunk_questions)

                        # 发送新题目到显示队列
                        self.question_queue.put({
                            'type': 'question',
                            'data': quiz_data
                        })

                        # 发送增量保存信息
                        if save_result.get('saved'):
                            self.question_queue.put({
                                'type': 'incremental_save',
                                'data': {
                                    'questions_saved': save_result.get('questions_saved', 0),
                                    'total_saved': save_result.get('total_saved', 0),
                                    'file_path': save_result.get('file_path', ''),
                                    'backup_created': save_result.get('backup_info', {}).get('created', False)
                                }
                            })
                        elif 'error' in save_result:
                            self.question_queue.put({
                                'type': 'incremental_save',
                                'data': {
                                    'questions_saved': save_result.get('questions_saved', 0),
                                    'total_saved': save_result.get('total_saved', 0),
                                    'file_path': save_result.get('file_path', ''),
                                    'backup_created': save_result.get('backup_info', {}).get('created', False),
                                    'error': save_result.get('error', '未知保存错误')
                                }
                            })

                        logger.debug(f"成功生成 {len(quiz_data['questions'])} 道题目")
                        i += 1  # 正常处理下一个分块

                    else:
                        # quiz_data不是None，但也没有questions键，说明格式错误
                        safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                        logger.warning(f"处理文本块失败: {safe_filename} - 原因: 响应格式错误")
                        failed_chunks.append({
                            **chunk,
                            'failure_reason': 'invalid_response_format'
                        })
                        self.question_queue.put({
                            'type': 'failure', # 从 failure_log 改为 failure
                            'data': {
                                'filename': chunk.get('filename', '未知文件'),
                                'chunk_index': chunk.get('chunk_index', 0),
                                'reason': '响应格式错误',
                                'chunk_content': chunk.get('content', '无内容') # 添加文本内容
                            }
                        })
                        i += 1

                except Exception as e:
                    # 安全获取chunk信息，防止chunk为None导致的错误
                    safe_filename = '未知文件'
                    safe_chunk_index = 0
                    safe_content = '无内容'

                    if chunk is not None and isinstance(chunk, dict):
                        safe_filename = chunk.get('filename', '未知文件')
                        safe_chunk_index = chunk.get('chunk_index', 0)
                        safe_content = chunk.get('content', '无内容')

                    logger.error(f"处理文本块失败: {safe_filename} - 原因: {str(e)}")

                    # 只有当chunk不为None时才添加到失败列表
                    if chunk is not None and isinstance(chunk, dict):
                        failed_chunks.append({
                            **chunk,
                            'failure_reason': 'processing_exception',
                            'error_message': str(e)
                        })
                    else:
                        # 如果chunk为None，创建一个基本的失败记录
                        failed_chunks.append({
                            'filename': safe_filename,
                            'chunk_index': safe_chunk_index,
                            'content': safe_content,
                            'failure_reason': 'processing_exception_null_chunk',
                            'error_message': str(e)
                        })

                    # 发送失败信息到显示队列 (只在控制台显示，GUI不弹窗)
                    self.question_queue.put({
                        'type': 'failure', # 从 failure_log 改为 failure
                        'data': {
                            'filename': safe_filename,
                            'chunk_index': safe_chunk_index,
                            'reason': f'处理异常: {str(e)}',
                            'chunk_content': safe_content # 添加文本内容
                        }
                    })
                    i += 1
                    continue

            # 强制保存剩余的题目
            final_save_result = processor.incremental_saver.force_save_all()
            if final_save_result.get('saved'):
                self.question_queue.put({
                    'type': 'final_save',
                    'data': {
                        'questions_saved': final_save_result.get('questions_saved', 0),
                        'total_saved': final_save_result.get('total_saved', 0),
                        'file_path': final_save_result.get('file_path', '')
                    }
                })

            # 4. 保存结果
            if all_questions:
                self.question_queue.put({
                    'type': 'status',
                    'data': '正在保存结果...'
                })

                result = processor._save_results(all_questions, failed_chunks, successful_chunk_indices)
                return result
            else:
                return {'error': '没有生成任何题目'}

        except Exception as e:
            # 安全获取文件名用于日志记录
            filename_for_log = '未知文件'

            # 检查chunk变量是否存在且不为None
            if 'chunk' in locals() and chunk is not None and isinstance(chunk, dict):
                filename_for_log = chunk.get('filename', '未知文件')

            logger.error(f"处理文本块失败: {filename_for_log} - 原因: {str(e)}")
            return {'error': str(e)}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错误日志功能
"""

import sys
import os
import tempfile
import traceback
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from error_handler.error_manager import ErrorManager

def test_chunk_failure_logging():
    """测试文本块失败日志记录"""
    print("测试: 文本块失败日志记录...")
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建配置
            config = Config()
            config.ERROR_DIR = os.path.join(temp_dir, "err_docs")
            
            # 创建错误管理器
            error_manager = ErrorManager(config)
            
            # 测试记录文本块失败
            error_manager._log_chunk_failure(
                filename="测试文件.docx",
                chunk_index=0,
                char_count=1500,
                error_message="内容不足以生成题目"
            )
            
            # 检查日志文件是否创建
            log_file = os.path.join(config.ERROR_DIR, 'chunk_failures.log')
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"  [OK] 日志文件创建成功，内容: {content.strip()}")
                    
                    # 验证内容
                    if "测试文件.docx" in content and "分块: 1" in content and "字数: 1500" in content:
                        print("  [OK] 日志内容验证成功")
                        return True
                    else:
                        print("  [FAIL] 日志内容验证失败")
                        return False
            else:
                print("  [FAIL] 日志文件未创建")
                return False
                
    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_safe_chunk_handling():
    """测试安全的chunk处理"""
    print("测试: 安全的chunk处理...")
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建配置
            config = Config()
            config.ERROR_DIR = os.path.join(temp_dir, "err_docs")
            
            # 创建错误管理器
            error_manager = ErrorManager(config)
            
            # 测试None chunk
            result = error_manager.save_failed_chunk(
                chunk=None,
                error_message="测试None chunk",
                has_generated_questions=True
            )
            
            if result == "":
                print("  [OK] None chunk处理成功")
            else:
                print("  [FAIL] None chunk处理失败")
                return False
            
            # 测试空字典chunk
            result = error_manager.save_failed_chunk(
                chunk={},
                error_message="测试空字典chunk",
                has_generated_questions=True
            )
            
            if result == "":
                print("  [OK] 空字典chunk处理成功")
            else:
                print("  [FAIL] 空字典chunk处理失败")
                return False
            
            # 检查日志文件
            log_file = os.path.join(config.ERROR_DIR, 'chunk_failures.log')
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"  [OK] 错误日志记录成功，共{len(content.splitlines())}条记录")
                    return True
            else:
                print("  [FAIL] 错误日志文件未创建")
                return False
                
    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_normal_chunk_handling():
    """测试正常chunk处理"""
    print("测试: 正常chunk处理...")
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建配置
            config = Config()
            config.ERROR_DIR = os.path.join(temp_dir, "err_docs")
            
            # 创建错误管理器
            error_manager = ErrorManager(config)
            
            # 测试正常chunk
            chunk = {
                'filename': '正常文件.docx',
                'chunk_index': 2,
                'content': '这是一个正常的文本块内容，用于测试',
                'char_count': 100
            }
            
            result = error_manager.save_failed_chunk(
                chunk=chunk,
                error_message="测试正常chunk",
                has_generated_questions=True
            )
            
            if result == "":
                print("  [OK] 正常chunk处理成功")
                
                # 检查日志文件
                log_file = os.path.join(config.ERROR_DIR, 'chunk_failures.log')
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if "正常文件.docx" in content and "分块: 3" in content:
                            print("  [OK] 正常chunk日志记录成功")
                            return True
                        else:
                            print("  [FAIL] 正常chunk日志内容错误")
                            return False
                else:
                    print("  [FAIL] 正常chunk日志文件未创建")
                    return False
            else:
                print("  [FAIL] 正常chunk处理失败")
                return False
                
    except Exception as e:
        print(f"  [FAIL] 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试错误日志功能...")
    print("=" * 50)
    
    tests = [
        test_chunk_failure_logging,
        test_safe_chunk_handling,
        test_normal_chunk_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"  [ERROR] 测试异常: {str(e)}")
            traceback.print_exc()
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过！错误日志功能正常。")
        return True
    else:
        print("[FAIL] 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

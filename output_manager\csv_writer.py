"""
CSV输出管理模块
负责将生成的题目保存为CSV格式
"""

import os
import csv
import logging
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class CSVWriter:
    """CSV文件写入器"""

    def __init__(self, config):
        """
        初始化CSV写入器

        Args:
            config: 配置对象
        """
        self.config = config
        self.output_dir = config.OUTPUT_DIR
        self.csv_filename = config.CSV_FILENAME
        self.csv_template_path = getattr(config, 'CSV_TEMPLATE_PATH', '试题导入模板.csv')

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 读取CSV模板结构
        self.csv_columns = self._read_csv_template()

        logger.info(f"CSV写入器初始化完成，输出目录: {self.output_dir}")
        logger.info(f"使用CSV模板: {self.csv_template_path}")

    def _read_csv_template(self) -> List[str]:
        """
        读取CSV模板文件，获取列结构

        Returns:
            CSV列名列表
        """
        try:
            if os.path.exists(self.csv_template_path):
                with open(self.csv_template_path, 'r', encoding='utf-8-sig') as f:
                    reader = csv.reader(f)
                    headers = next(reader)  # 读取第一行作为列名
                    # 清理列名，去除空白字符
                    headers = [header.strip() for header in headers if header.strip()]
                    logger.info(f"从模板文件读取到 {len(headers)} 个列: {headers}")
                    return headers
            else:
                logger.warning(f"CSV模板文件不存在: {self.csv_template_path}，使用默认列结构")
                return [
                    '试题题干(必填)', '试题类型(必填，题型请用下拉菜单实现）', '选项（用\'|\'隔开）',
                    '答案（填空题用\'|\'隔开）(必填)', '分数', '难易度 (必填，难易度请选择下拉菜单实现)',
                    '试题解析', '归属目录编码（必填）', '归属部门编码（必填）'
                ]
        except Exception as e:
            logger.error(f"读取CSV模板文件失败: {str(e)}，使用默认列结构")
            return [
                '试题题干(必填)', '试题类型(必填，题型请用下拉菜单实现）', '选项（用\'|\'隔开）',
                '答案（填空题用\'|\'隔开）(必填)', '分数', '难易度 (必填，难易度请选择下拉菜单实现)',
                '试题解析', '归属目录编码（必填）', '归属部门编码（必填）'
            ]

    def save_questions_to_csv(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        保存题目到CSV文件

        Args:
            questions: 题目列表
            filename: 输出文件名，默认使用配置中的文件名

        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = self.csv_filename

        file_path = os.path.join(self.output_dir, filename)

        try:
            # 转换题目数据为CSV格式
            csv_data = self._convert_questions_to_csv_format(questions)

            # 写入CSV文件
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.csv_columns)
                writer.writeheader()
                writer.writerows(csv_data)

            logger.info(f"成功保存 {len(questions)} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
            raise

    def append_questions_to_csv(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        追加题目到现有CSV文件

        Args:
            questions: 题目列表
            filename: 文件名

        Returns:
            文件路径
        """
        if filename is None:
            filename = self.csv_filename

        file_path = os.path.join(self.output_dir, filename)

        try:
            # 转换题目数据
            csv_data = self._convert_questions_to_csv_format(questions)

            # 检查文件是否存在
            file_exists = os.path.exists(file_path)

            with open(file_path, 'a', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.csv_columns)

                # 如果文件不存在，写入表头
                if not file_exists:
                    writer.writeheader()

                writer.writerows(csv_data)

            logger.info(f"成功追加 {len(questions)} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"追加CSV文件失败: {str(e)}")
            raise

    def _convert_questions_to_csv_format(self, questions: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """
        将题目数据转换为CSV格式

        Args:
            questions: 题目列表

        Returns:
            CSV格式的数据列表
        """
        csv_data = []

        for question in questions:
            try:
                # 根据CSV模板格式转换数据
                csv_row = {}

                # 初始化所有列为空
                for col in self.csv_columns:
                    csv_row[col] = ''

                # 填充数据
                question_text = question.get('question', '')
                question_type = question.get('type', '')
                options = question.get('options', '')
                answer = question.get('answer', '')
                explanation = question.get('explanation', '')
                score = question.get('score', '1')
                difficulty = question.get('difficulty', '低')

                # 映射到CSV模板列
                if '试题题干(必填)' in csv_row:
                    csv_row['试题题干(必填)'] = question_text
                if '试题类型(必填，题型请用下拉菜单实现）' in csv_row:
                    csv_row['试题类型(必填，题型请用下拉菜单实现）'] = question_type
                if '选项（用\'|\'隔开）' in csv_row:
                    csv_row['选项（用\'|\'隔开）'] = options
                if '答案（填空题用\'|\'隔开）(必填)' in csv_row:
                    csv_row['答案（填空题用\'|\'隔开）(必填)'] = answer
                if '分数' in csv_row:
                    csv_row['分数'] = score
                if '难易度 (必填，难易度请选择下拉菜单实现)' in csv_row:
                    csv_row['难易度 (必填，难易度请选择下拉菜单实现)'] = difficulty
                if '试题解析' in csv_row:
                    csv_row['试题解析'] = explanation
                if '归属目录编码（必填）' in csv_row:
                    csv_row['归属目录编码（必填）'] = ''
                if '归属部门编码（必填）' in csv_row:
                    csv_row['归属部门编码（必填）'] = ''

                csv_data.append(csv_row)

            except Exception as e:
                logger.error(f"转换题目数据失败: {str(e)}")
                continue

        return csv_data

    def batch_save_questions(self, questions: List[Dict[str, Any]], batch_size: int = None, filename: str = None) -> str:
        """
        批量保存题目，支持分批写入

        Args:
            questions: 题目列表
            batch_size: 批量大小，默认使用配置中的值
            filename: 输出文件名

        Returns:
            保存的文件路径
        """
        if batch_size is None:
            batch_size = getattr(self.config, 'CSV_BATCH_SAVE_COUNT', 5)

        if filename is None:
            filename = self.csv_filename

        file_path = os.path.join(self.output_dir, filename)
        total_saved = 0

        try:
            # 分批处理
            for i in range(0, len(questions), batch_size):
                batch_questions = questions[i:i + batch_size]

                if i == 0:
                    # 第一批，创建新文件
                    self.save_questions_to_csv(batch_questions, filename)
                else:
                    # 后续批次，追加到文件
                    self.append_questions_to_csv(batch_questions, filename)

                total_saved += len(batch_questions)
                logger.info(f"已保存 {total_saved}/{len(questions)} 道题目")

            logger.info(f"批量保存完成，共保存 {total_saved} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"批量保存失败: {str(e)}")
            raise

    def create_progress_backup(self, questions: List[Dict[str, Any]], backup_suffix: str = None) -> str:
        """
        创建进度备份文件

        Args:
            questions: 题目列表
            backup_suffix: 备份文件后缀

        Returns:
            备份文件路径
        """
        if backup_suffix is None:
            backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")

        backup_filename = f"quiz_backup_{backup_suffix}.csv"
        return self.save_questions_to_csv(questions, backup_filename)

    def merge_csv_files(self, csv_files: List[str], output_filename: str = None) -> str:
        """
        合并多个CSV文件

        Args:
            csv_files: CSV文件路径列表
            output_filename: 输出文件名

        Returns:
            合并后的文件路径
        """
        if output_filename is None:
            output_filename = f"merged_quiz_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            all_data = []

            for csv_file in csv_files:
                if os.path.exists(csv_file):
                    df = pd.read_csv(csv_file, encoding='utf-8-sig')
                    all_data.append(df)
                    logger.info(f"读取CSV文件: {csv_file}")

            if all_data:
                merged_df = pd.concat(all_data, ignore_index=True)
                merged_df.to_csv(output_path, index=False, encoding='utf-8-sig')
                logger.info(f"成功合并 {len(csv_files)} 个CSV文件到 {output_path}")
                return output_path
            else:
                logger.warning("没有找到有效的CSV文件进行合并")
                return ""

        except Exception as e:
            logger.error(f"合并CSV文件失败: {str(e)}")
            raise

    def generate_summary_report(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成处理摘要报告

        Args:
            questions: 题目列表

        Returns:
            摘要报告
        """
        if not questions:
            return {
                'total_questions': 0,
                'files_processed': 0,
                'question_types': {},
                'processing_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        # 统计信息
        files = set()
        question_types = {}

        for question in questions:
            # 统计文件
            source_file = question.get('source_file', '')
            if source_file:
                files.add(source_file)

            # 统计题目类型
            q_type = question.get('type', '未知')
            question_types[q_type] = question_types.get(q_type, 0) + 1

        summary = {
            'total_questions': len(questions),
            'files_processed': len(files),
            'processed_files': list(files),
            'question_types': question_types,
            'processing_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return summary

    def save_summary_report(self, summary: Dict[str, Any], filename: str = None) -> str:
        """
        保存摘要报告

        Args:
            summary: 摘要数据
            filename: 文件名

        Returns:
            报告文件路径
        """
        if filename is None:
            filename = f"processing_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        file_path = os.path.join(self.output_dir, filename)

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("=== 文档到题库处理摘要报告 ===\n\n")
                f.write(f"处理时间: {summary['processing_time']}\n")
                f.write(f"总题目数: {summary['total_questions']}\n")
                f.write(f"处理文件数: {summary['files_processed']}\n\n")

                f.write("处理的文件列表:\n")
                for file in summary.get('processed_files', []):
                    f.write(f"  - {file}\n")

                f.write("\n题目类型统计:\n")
                for q_type, count in summary.get('question_types', {}).items():
                    f.write(f"  - {q_type}: {count}道\n")

            logger.info(f"摘要报告已保存到: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"保存摘要报告失败: {str(e)}")
            raise

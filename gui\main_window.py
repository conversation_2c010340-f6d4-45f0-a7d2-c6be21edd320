"""
主GUI窗口
使用tkinter和customtkinter创建现代化界面
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import logging
from typing import Dict, Any

# 尝试导入customtkinter，如果没有则使用标准tkinter
try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except ImportError:
    CTK_AVAILABLE = False

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config
from main import DocumentToQuizProcessor

class DocumentToQuizGUI:
    """文档到题库工具GUI主窗口"""

    def __init__(self):
        """初始化GUI"""
        # 确保有默认配置文件
        Config.create_default_config_if_missing()

        self.config = Config.load_config()
        self.processor = None
        self.processing_thread = None
        self.log_queue = queue.Queue()

        # 创建主窗口
        if CTK_AVAILABLE:
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")
            self.root = ctk.CTk()
        else:
            self.root = tk.Tk()

        self.root.title("文档到题库转换工具")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        self.setup_ui()
        self.setup_logging()

        # 启动日志处理
        self.root.after(100, self.process_log_queue)

        # 检查配置状态
        self.root.after(500, self.check_initial_config)

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        if CTK_AVAILABLE:
            main_frame = ctk.CTkFrame(self.root)
        else:
            main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 创建标签页
        if CTK_AVAILABLE:
            self.notebook = ctk.CTkTabview(main_frame)
        else:
            self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)

        # 创建各个标签页
        self.create_main_tab()
        self.create_config_tab()
        self.create_log_tab()
        self.create_about_tab()

    def create_main_tab(self):
        """创建主要功能标签页"""
        if CTK_AVAILABLE:
            main_tab = self.notebook.add("主要功能")
            main_frame = ctk.CTkFrame(main_tab)
        else:
            main_tab = ttk.Frame(self.notebook)
            self.notebook.add(main_tab, text="主要功能")
            main_frame = ttk.Frame(main_tab)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 输入目录选择
        input_frame = self.create_labeled_frame(main_frame, "输入设置")
        input_frame.pack(fill="x", pady=(0, 10))

        # 输入目录
        if CTK_AVAILABLE:
            ctk.CTkLabel(input_frame, text="文档目录:").pack(anchor="w")
            input_dir_frame = ctk.CTkFrame(input_frame)
        else:
            ttk.Label(input_frame, text="文档目录:").pack(anchor="w")
            input_dir_frame = ttk.Frame(input_frame)
        input_dir_frame.pack(fill="x", pady=(5, 0))

        if CTK_AVAILABLE:
            self.input_dir_var = tk.StringVar()
            self.input_dir_entry = ctk.CTkEntry(input_dir_frame, textvariable=self.input_dir_var)
            self.input_dir_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
            ctk.CTkButton(input_dir_frame, text="浏览", command=self.browse_input_dir, width=80).pack(side="right")
        else:
            self.input_dir_var = tk.StringVar()
            self.input_dir_entry = ttk.Entry(input_dir_frame, textvariable=self.input_dir_var)
            self.input_dir_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
            ttk.Button(input_dir_frame, text="浏览", command=self.browse_input_dir).pack(side="right")

        # 输出目录
        if CTK_AVAILABLE:
            ctk.CTkLabel(input_frame, text="输出目录:").pack(anchor="w", pady=(10, 0))
            output_dir_frame = ctk.CTkFrame(input_frame)
        else:
            ttk.Label(input_frame, text="输出目录:").pack(anchor="w", pady=(10, 0))
            output_dir_frame = ttk.Frame(input_frame)
        output_dir_frame.pack(fill="x", pady=(5, 0))

        if CTK_AVAILABLE:
            self.output_dir_var = tk.StringVar(value=self.config.OUTPUT_DIR)
            self.output_dir_entry = ctk.CTkEntry(output_dir_frame, textvariable=self.output_dir_var)
            self.output_dir_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
            ctk.CTkButton(output_dir_frame, text="浏览", command=self.browse_output_dir, width=80).pack(side="right")
        else:
            self.output_dir_var = tk.StringVar(value=self.config.OUTPUT_DIR)
            self.output_dir_entry = ttk.Entry(output_dir_frame, textvariable=self.output_dir_var)
            self.output_dir_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
            ttk.Button(output_dir_frame, text="浏览", command=self.browse_output_dir).pack(side="right")

        # 处理选项
        options_frame = self.create_labeled_frame(main_frame, "处理选项")
        options_frame.pack(fill="x", pady=(0, 10))

        # 创建两列布局
        if CTK_AVAILABLE:
            left_col = ctk.CTkFrame(options_frame)
            right_col = ctk.CTkFrame(options_frame)
        else:
            left_col = ttk.Frame(options_frame)
            right_col = ttk.Frame(options_frame)
        left_col.pack(side="left", fill="both", expand=True, padx=(0, 5))
        right_col.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # 左列选项
        if CTK_AVAILABLE:
            ctk.CTkLabel(left_col, text="每块题目数量:").pack(anchor="w")
            self.questions_per_chunk_var = tk.StringVar(value=str(self.config.QUESTIONS_PER_CHUNK))
            ctk.CTkEntry(left_col, textvariable=self.questions_per_chunk_var).pack(fill="x", pady=(5, 10))

            ctk.CTkLabel(left_col, text="文本块大小:").pack(anchor="w")
            self.chunk_size_var = tk.StringVar(value=str(self.config.MAX_CHUNK_SIZE))
            ctk.CTkEntry(left_col, textvariable=self.chunk_size_var).pack(fill="x", pady=(5, 0))
        else:
            ttk.Label(left_col, text="每块题目数量:").pack(anchor="w")
            self.questions_per_chunk_var = tk.StringVar(value=str(self.config.QUESTIONS_PER_CHUNK))
            ttk.Entry(left_col, textvariable=self.questions_per_chunk_var).pack(fill="x", pady=(5, 10))

            ttk.Label(left_col, text="文本块大小:").pack(anchor="w")
            self.chunk_size_var = tk.StringVar(value=str(self.config.MAX_CHUNK_SIZE))
            ttk.Entry(left_col, textvariable=self.chunk_size_var).pack(fill="x", pady=(5, 0))

        # 右列选项
        if CTK_AVAILABLE:
            self.resume_var = tk.BooleanVar()
            ctk.CTkCheckBox(right_col, text="从中断处继续", variable=self.resume_var).pack(anchor="w", pady=(0, 10))

            self.verbose_var = tk.BooleanVar()
            ctk.CTkCheckBox(right_col, text="详细日志", variable=self.verbose_var).pack(anchor="w")
        else:
            self.resume_var = tk.BooleanVar()
            ttk.Checkbutton(right_col, text="从中断处继续", variable=self.resume_var).pack(anchor="w", pady=(0, 10))

            self.verbose_var = tk.BooleanVar()
            ttk.Checkbutton(right_col, text="详细日志", variable=self.verbose_var).pack(anchor="w")

        # 控制按钮
        button_frame = self.create_labeled_frame(main_frame, "操作")
        button_frame.pack(fill="x", pady=(0, 10))

        if CTK_AVAILABLE:
            btn_frame = ctk.CTkFrame(button_frame)
        else:
            btn_frame = ttk.Frame(button_frame)
        btn_frame.pack(fill="x")

        if CTK_AVAILABLE:
            self.test_btn = ctk.CTkButton(btn_frame, text="测试连接", command=self.test_connection)
            self.test_btn.pack(side="left", padx=(0, 10))

            self.start_btn = ctk.CTkButton(btn_frame, text="开始处理", command=self.start_processing)
            self.start_btn.pack(side="left", padx=(0, 10))

            self.stop_btn = ctk.CTkButton(btn_frame, text="停止处理", command=self.stop_processing, state="disabled")
            self.stop_btn.pack(side="left")
        else:
            self.test_btn = ttk.Button(btn_frame, text="测试连接", command=self.test_connection)
            self.test_btn.pack(side="left", padx=(0, 10))

            self.start_btn = ttk.Button(btn_frame, text="开始处理", command=self.start_processing)
            self.start_btn.pack(side="left", padx=(0, 10))

            self.stop_btn = ttk.Button(btn_frame, text="停止处理", command=self.stop_processing, state="disabled")
            self.stop_btn.pack(side="left")

        # 进度显示
        progress_frame = self.create_labeled_frame(main_frame, "进度")
        progress_frame.pack(fill="x")

        if CTK_AVAILABLE:
            self.progress_var = tk.StringVar(value="就绪")
            self.progress_label = ctk.CTkLabel(progress_frame, textvariable=self.progress_var)
            self.progress_label.pack(anchor="w")

            self.progress_bar = ctk.CTkProgressBar(progress_frame)
            self.progress_bar.pack(fill="x", pady=(5, 0))
            self.progress_bar.set(0)
        else:
            self.progress_var = tk.StringVar(value="就绪")
            self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
            self.progress_label.pack(anchor="w")

            self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
            self.progress_bar.pack(fill="x", pady=(5, 0))

    def create_labeled_frame(self, parent, title):
        """创建带标题的框架"""
        if CTK_AVAILABLE:
            frame = ctk.CTkFrame(parent)
            label = ctk.CTkLabel(frame, text=title, font=ctk.CTkFont(size=14, weight="bold"))
            label.pack(anchor="w", padx=10, pady=(10, 5))
            content_frame = ctk.CTkFrame(frame)
            content_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
            return content_frame
        else:
            labelframe = ttk.LabelFrame(parent, text=title)
            return labelframe

    def create_config_tab(self):
        """创建配置标签页"""
        if CTK_AVAILABLE:
            config_tab = self.notebook.add("配置设置")
            config_frame = ctk.CTkScrollableFrame(config_tab)
        else:
            config_tab = ttk.Frame(self.notebook)
            self.notebook.add(config_tab, text="配置设置")

            # 创建滚动框架
            canvas = tk.Canvas(config_tab)
            scrollbar = ttk.Scrollbar(config_tab, orient="vertical", command=canvas.yview)
            config_frame = ttk.Frame(canvas)

            config_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=config_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        config_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # API配置
        api_frame = self.create_labeled_frame(config_frame, "API配置")
        api_frame.pack(fill="x", pady=(0, 10))

        # API基础URL
        if CTK_AVAILABLE:
            ctk.CTkLabel(api_frame, text="API基础URL:").pack(anchor="w")
            self.api_base_var = tk.StringVar(value=self.config.API_BASE_URL)
            ctk.CTkEntry(api_frame, textvariable=self.api_base_var).pack(fill="x", pady=(5, 10))
        else:
            ttk.Label(api_frame, text="API基础URL:").pack(anchor="w")
            self.api_base_var = tk.StringVar(value=self.config.API_BASE_URL)
            ttk.Entry(api_frame, textvariable=self.api_base_var).pack(fill="x", pady=(5, 10))

        # API密钥
        if CTK_AVAILABLE:
            ctk.CTkLabel(api_frame, text="API密钥:").pack(anchor="w")
            self.api_key_var = tk.StringVar(value=self.config.API_KEY)
            ctk.CTkEntry(api_frame, textvariable=self.api_key_var, show="*").pack(fill="x", pady=(5, 10))
        else:
            ttk.Label(api_frame, text="API密钥:").pack(anchor="w")
            self.api_key_var = tk.StringVar(value=self.config.API_KEY)
            ttk.Entry(api_frame, textvariable=self.api_key_var, show="*").pack(fill="x", pady=(5, 10))

        # 模型名称
        if CTK_AVAILABLE:
            ctk.CTkLabel(api_frame, text="模型名称:").pack(anchor="w")
            self.model_var = tk.StringVar(value=self.config.MODEL_NAME)
            ctk.CTkEntry(api_frame, textvariable=self.model_var).pack(fill="x", pady=(5, 10))
        else:
            ttk.Label(api_frame, text="模型名称:").pack(anchor="w")
            self.model_var = tk.StringVar(value=self.config.MODEL_NAME)
            ttk.Entry(api_frame, textvariable=self.model_var).pack(fill="x", pady=(5, 10))

        # 高级API设置
        advanced_frame = self.create_labeled_frame(config_frame, "高级API设置")
        advanced_frame.pack(fill="x", pady=(0, 10))

        # 创建两列
        if CTK_AVAILABLE:
            left_adv = ctk.CTkFrame(advanced_frame)
            right_adv = ctk.CTkFrame(advanced_frame)
        else:
            left_adv = ttk.Frame(advanced_frame)
            right_adv = ttk.Frame(advanced_frame)
        left_adv.pack(side="left", fill="both", expand=True, padx=(0, 5))
        right_adv.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # 左列
        if CTK_AVAILABLE:
            ctk.CTkLabel(left_adv, text="最大Token数:").pack(anchor="w")
            self.max_tokens_var = tk.StringVar(value=str(self.config.MAX_TOKENS))
            ctk.CTkEntry(left_adv, textvariable=self.max_tokens_var).pack(fill="x", pady=(5, 10))

            ctk.CTkLabel(left_adv, text="温度 (0-1):").pack(anchor="w")
            self.temperature_var = tk.StringVar(value=str(self.config.TEMPERATURE))
            ctk.CTkEntry(left_adv, textvariable=self.temperature_var).pack(fill="x", pady=(5, 0))
        else:
            ttk.Label(left_adv, text="最大Token数:").pack(anchor="w")
            self.max_tokens_var = tk.StringVar(value=str(self.config.MAX_TOKENS))
            ttk.Entry(left_adv, textvariable=self.max_tokens_var).pack(fill="x", pady=(5, 10))

            ttk.Label(left_adv, text="温度 (0-1):").pack(anchor="w")
            self.temperature_var = tk.StringVar(value=str(self.config.TEMPERATURE))
            ttk.Entry(left_adv, textvariable=self.temperature_var).pack(fill="x", pady=(5, 0))

        # 右列
        if CTK_AVAILABLE:
            ctk.CTkLabel(right_adv, text="请求超时(秒):").pack(anchor="w")
            self.timeout_var = tk.StringVar(value=str(self.config.REQUEST_TIMEOUT))
            ctk.CTkEntry(right_adv, textvariable=self.timeout_var).pack(fill="x", pady=(5, 10))

            ctk.CTkLabel(right_adv, text="最大重试次数:").pack(anchor="w")
            self.max_retries_var = tk.StringVar(value=str(self.config.MAX_RETRIES))
            ctk.CTkEntry(right_adv, textvariable=self.max_retries_var).pack(fill="x", pady=(5, 0))
        else:
            ttk.Label(right_adv, text="请求超时(秒):").pack(anchor="w")
            self.timeout_var = tk.StringVar(value=str(self.config.REQUEST_TIMEOUT))
            ttk.Entry(right_adv, textvariable=self.timeout_var).pack(fill="x", pady=(5, 10))

            ttk.Label(right_adv, text="最大重试次数:").pack(anchor="w")
            self.max_retries_var = tk.StringVar(value=str(self.config.MAX_RETRIES))
            ttk.Entry(right_adv, textvariable=self.max_retries_var).pack(fill="x", pady=(5, 0))

        # 配置文件操作
        file_frame = self.create_labeled_frame(config_frame, "配置文件")
        file_frame.pack(fill="x", pady=(0, 10))

        if CTK_AVAILABLE:
            btn_frame = ctk.CTkFrame(file_frame)
        else:
            btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(fill="x")

        if CTK_AVAILABLE:
            ctk.CTkButton(btn_frame, text="保存配置", command=self.save_current_config).pack(side="left", padx=(0, 10))
            ctk.CTkButton(btn_frame, text="加载配置", command=self.load_config_file).pack(side="left", padx=(0, 10))
            ctk.CTkButton(btn_frame, text="另存为", command=self.save_config_file).pack(side="left", padx=(0, 10))
            ctk.CTkButton(btn_frame, text="重置默认", command=self.reset_config).pack(side="left")
        else:
            ttk.Button(btn_frame, text="保存配置", command=self.save_current_config).pack(side="left", padx=(0, 10))
            ttk.Button(btn_frame, text="加载配置", command=self.load_config_file).pack(side="left", padx=(0, 10))
            ttk.Button(btn_frame, text="另存为", command=self.save_config_file).pack(side="left", padx=(0, 10))
            ttk.Button(btn_frame, text="重置默认", command=self.reset_config).pack(side="left")

    def create_log_tab(self):
        """创建日志标签页"""
        if CTK_AVAILABLE:
            log_tab = self.notebook.add("运行日志")
            log_frame = ctk.CTkFrame(log_tab)
        else:
            log_tab = ttk.Frame(self.notebook)
            self.notebook.add(log_tab, text="运行日志")
            log_frame = ttk.Frame(log_tab)
        log_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 日志控制
        if CTK_AVAILABLE:
            control_frame = ctk.CTkFrame(log_frame)
        else:
            control_frame = ttk.Frame(log_frame)
        control_frame.pack(fill="x", pady=(0, 10))

        if CTK_AVAILABLE:
            ctk.CTkButton(control_frame, text="清空日志", command=self.clear_log).pack(side="left", padx=(0, 10))
            ctk.CTkButton(control_frame, text="保存日志", command=self.save_log).pack(side="left")
        else:
            ttk.Button(control_frame, text="清空日志", command=self.clear_log).pack(side="left", padx=(0, 10))
            ttk.Button(control_frame, text="保存日志", command=self.save_log).pack(side="left")

        # 日志显示区域
        if CTK_AVAILABLE:
            self.log_text = ctk.CTkTextbox(log_frame)
        else:
            self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD)
        self.log_text.pack(fill="both", expand=True)

    def create_about_tab(self):
        """创建关于标签页"""
        if CTK_AVAILABLE:
            about_tab = self.notebook.add("关于")
            about_frame = ctk.CTkFrame(about_tab)
        else:
            about_tab = ttk.Frame(self.notebook)
            self.notebook.add(about_tab, text="关于")
            about_frame = ttk.Frame(about_tab)
        about_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        if CTK_AVAILABLE:
            title_label = ctk.CTkLabel(about_frame, text="文档到题库转换工具",
                                     font=ctk.CTkFont(size=24, weight="bold"))
        else:
            title_label = ttk.Label(about_frame, text="文档到题库转换工具",
                                  font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))

        # 版本信息
        if CTK_AVAILABLE:
            version_label = ctk.CTkLabel(about_frame, text="版本: 1.0.0",
                                       font=ctk.CTkFont(size=14))
        else:
            version_label = ttk.Label(about_frame, text="版本: 1.0.0")
        version_label.pack(pady=(0, 10))

        # 功能描述
        description = """
这是一个强大的文档处理工具，能够：

• 读取DOC和DOCX格式文档
• 智能分割文本内容，保持语义完整性
• 调用OpenAI兼容的API生成题库
• 输出标准CSV格式题库文件
• 支持断点续传和错误处理
• 提供图形化界面，操作简单直观

支持多种OpenAI兼容的API服务商，
包括OpenAI、DeepSeek、智谱AI、月之暗面等。
        """

        if CTK_AVAILABLE:
            desc_label = ctk.CTkLabel(about_frame, text=description,
                                    font=ctk.CTkFont(size=12), justify="left")
        else:
            desc_label = ttk.Label(about_frame, text=description, justify="left")
        desc_label.pack(pady=(0, 20))

        # 使用说明
        if CTK_AVAILABLE:
            help_label = ctk.CTkLabel(about_frame, text="使用说明：",
                                    font=ctk.CTkFont(size=14, weight="bold"))
        else:
            help_label = ttk.Label(about_frame, text="使用说明：",
                                 font=("Arial", 12, "bold"))
        help_label.pack(anchor="w", pady=(0, 10))

        help_text = """1. 在"配置设置"页面配置API信息
2. 在"主要功能"页面选择文档目录
3. 调整处理参数（可选）
4. 点击"测试连接"验证API配置
5. 点击"开始处理"开始转换
6. 在"运行日志"页面查看处理进度"""

        if CTK_AVAILABLE:
            help_content = ctk.CTkLabel(about_frame, text=help_text,
                                      font=ctk.CTkFont(size=12), justify="left")
        else:
            help_content = ttk.Label(about_frame, text=help_text, justify="left")
        help_content.pack(anchor="w")

    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        self.log_handler = GUILogHandler(self.log_queue)
        self.log_handler.setLevel(logging.INFO)

        # 获取根日志器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(self.log_handler)
        root_logger.setLevel(logging.INFO)

    def process_log_queue(self):
        """处理日志队列"""
        try:
            while True:
                record = self.log_queue.get_nowait()
                self.append_log(record)
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.process_log_queue)

    def append_log(self, message):
        """添加日志消息"""
        if CTK_AVAILABLE:
            self.log_text.insert("end", f"{message}\n")
            self.log_text.see("end")
        else:
            self.log_text.insert(tk.END, f"{message}\n")
            self.log_text.see(tk.END)

    # 事件处理方法
    def browse_input_dir(self):
        """浏览输入目录"""
        directory = filedialog.askdirectory(title="选择文档目录")
        if directory:
            self.input_dir_var.set(directory)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir_var.set(directory)

    def update_config_from_gui(self):
        """从GUI更新配置"""
        try:
            # API配置
            self.config.API_BASE_URL = self.api_base_var.get()
            self.config.API_KEY = self.api_key_var.get()
            self.config.MODEL_NAME = self.model_var.get()
            self.config.MAX_TOKENS = int(self.max_tokens_var.get())
            self.config.TEMPERATURE = float(self.temperature_var.get())
            self.config.REQUEST_TIMEOUT = int(self.timeout_var.get())
            self.config.MAX_RETRIES = int(self.max_retries_var.get())

            # 处理配置
            self.config.QUESTIONS_PER_CHUNK = int(self.questions_per_chunk_var.get())
            self.config.MAX_CHUNK_SIZE = int(self.chunk_size_var.get())
            self.config.OUTPUT_DIR = self.output_dir_var.get()

            return True
        except ValueError as e:
            messagebox.showerror("配置错误", f"配置参数格式错误: {str(e)}")
            return False

    def update_gui_from_config(self):
        """从配置更新GUI"""
        # API配置
        self.api_base_var.set(self.config.API_BASE_URL)
        self.api_key_var.set(self.config.API_KEY)
        self.model_var.set(self.config.MODEL_NAME)
        self.max_tokens_var.set(str(self.config.MAX_TOKENS))
        self.temperature_var.set(str(self.config.TEMPERATURE))
        self.timeout_var.set(str(self.config.REQUEST_TIMEOUT))
        self.max_retries_var.set(str(self.config.MAX_RETRIES))

        # 处理配置
        self.questions_per_chunk_var.set(str(self.config.QUESTIONS_PER_CHUNK))
        self.chunk_size_var.set(str(self.config.MAX_CHUNK_SIZE))
        self.output_dir_var.set(self.config.OUTPUT_DIR)

    def test_connection(self):
        """测试API连接"""
        if not self.update_config_from_gui():
            return

        try:
            self.processor = DocumentToQuizProcessor(self.config)
            if self.processor.test_api_connection():
                messagebox.showinfo("连接测试", "✅ API连接测试成功！")
                self.append_log("API连接测试成功")
            else:
                messagebox.showerror("连接测试", "❌ API连接测试失败，请检查配置")
                self.append_log("API连接测试失败")
        except Exception as e:
            messagebox.showerror("连接测试", f"连接测试异常: {str(e)}")
            self.append_log(f"连接测试异常: {str(e)}")

    def start_processing(self):
        """开始处理文档"""
        if not self.update_config_from_gui():
            return

        input_dir = self.input_dir_var.get()
        if not input_dir:
            messagebox.showerror("错误", "请选择输入文档目录")
            return

        if not os.path.exists(input_dir):
            messagebox.showerror("错误", "输入目录不存在")
            return

        # 禁用开始按钮，启用停止按钮
        if CTK_AVAILABLE:
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
        else:
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")

        # 重置进度
        if CTK_AVAILABLE:
            self.progress_bar.set(0)
        else:
            self.progress_bar['value'] = 0

        self.progress_var.set("开始处理...")

        # 在新线程中处理
        self.processing_thread = threading.Thread(
            target=self._process_documents_thread,
            args=(input_dir, self.resume_var.get()),
            daemon=True
        )
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.is_alive():
            # 这里可以添加停止处理的逻辑
            self.append_log("用户请求停止处理...")
            self.progress_var.set("正在停止...")

        # 恢复按钮状态
        if CTK_AVAILABLE:
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
        else:
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")

    def _process_documents_thread(self, input_dir, resume):
        """在后台线程中处理文档"""
        try:
            self.processor = DocumentToQuizProcessor(self.config)
            result = self.processor.process_documents(input_dir, resume)

            # 更新进度
            self.root.after(0, self._processing_completed, result)

        except Exception as e:
            self.root.after(0, self._processing_error, str(e))

    def _processing_completed(self, result):
        """处理完成回调"""
        if 'error' in result:
            messagebox.showerror("处理失败", f"处理失败: {result['error']}")
            self.progress_var.set("处理失败")
        else:
            messagebox.showinfo("处理完成",
                              f"处理完成！\n"
                              f"总题目数: {result['total_questions']}\n"
                              f"处理文件数: {result['processed_files_count']}\n"
                              f"成功率: {result['success_rate']:.1f}%")
            self.progress_var.set("处理完成")

            if CTK_AVAILABLE:
                self.progress_bar.set(1.0)
            else:
                self.progress_bar['value'] = 100

        # 恢复按钮状态
        if CTK_AVAILABLE:
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
        else:
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")

    def _processing_error(self, error_msg):
        """处理错误回调"""
        messagebox.showerror("处理错误", f"处理过程中发生错误: {error_msg}")
        self.progress_var.set("处理错误")

        # 恢复按钮状态
        if CTK_AVAILABLE:
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
        else:
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")

    # 配置文件操作方法
    def load_config_file(self):
        """加载配置文件"""
        file_path = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json"), ("INI文件", "*.ini"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    self.config = Config.from_json_file(file_path)
                else:
                    self.config = Config.from_config_file(file_path)

                self.update_gui_from_config()
                messagebox.showinfo("成功", "配置文件加载成功！")
                self.append_log(f"配置文件加载成功: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
                self.append_log(f"加载配置文件失败: {str(e)}")

    def save_current_config(self):
        """保存当前配置到默认配置文件"""
        if not self.update_config_from_gui():
            return

        try:
            # 保存到默认的config.json文件
            self.config.save_to_json_file("config.json")
            messagebox.showinfo("成功", "配置已保存到 config.json！")
            self.append_log("配置已保存到 config.json")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            self.append_log(f"保存配置失败: {str(e)}")

    def save_config_file(self):
        """另存为配置文件"""
        if not self.update_config_from_gui():
            return

        file_path = filedialog.asksaveasfilename(
            title="另存为配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("INI文件", "*.ini"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    self.config.save_to_json_file(file_path)
                else:
                    self.config.save_to_config_file(file_path)

                messagebox.showinfo("成功", "配置文件保存成功！")
                self.append_log(f"配置文件保存成功: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")
                self.append_log(f"保存配置文件失败: {str(e)}")

    def reset_config(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？"):
            self.config = Config()
            self.update_gui_from_config()
            messagebox.showinfo("成功", "配置已重置为默认值！")
            self.append_log("配置已重置为默认值")

    # 日志操作方法
    def clear_log(self):
        """清空日志"""
        if CTK_AVAILABLE:
            self.log_text.delete("1.0", "end")
        else:
            self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                if CTK_AVAILABLE:
                    log_content = self.log_text.get("1.0", "end")
                else:
                    log_content = self.log_text.get(1.0, tk.END)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(log_content)

                messagebox.showinfo("成功", "日志文件保存成功！")

            except Exception as e:
                messagebox.showerror("错误", f"保存日志文件失败: {str(e)}")

    def check_initial_config(self):
        """检查初始配置状态"""
        if not self.config.is_api_configured():
            missing_items = self.config.get_missing_config_items()
            message = f"检测到配置不完整，缺少以下项目：\n\n"
            message += "\n".join(f"• {item}" for item in missing_items)
            message += "\n\n是否现在进行配置？"

            if messagebox.askyesno("配置不完整", message):
                self.show_config_wizard()
            else:
                self.append_log("⚠️ 配置不完整，部分功能可能无法使用")

    def show_config_wizard(self):
        """显示配置向导"""
        ConfigWizardWindow(self.root, self.config, self.update_config_callback)

    def update_config_callback(self, new_config):
        """配置更新回调"""
        self.config = new_config
        self.update_gui_from_config()
        self.append_log("✅ 配置已更新")
        messagebox.showinfo("配置完成", "配置已成功更新！")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

class ConfigWizardWindow:
    """配置向导窗口"""

    def __init__(self, parent, config, callback):
        self.parent = parent
        self.config = config
        self.callback = callback

        # 创建向导窗口
        if CTK_AVAILABLE:
            self.window = ctk.CTkToplevel(parent)
        else:
            self.window = tk.Toplevel(parent)

        self.window.title("配置向导")
        self.window.geometry("600x500")
        self.window.transient(parent)
        self.window.grab_set()

        self.setup_wizard_ui()

    def setup_wizard_ui(self):
        """设置向导界面"""
        # 主框架
        if CTK_AVAILABLE:
            main_frame = ctk.CTkFrame(self.window)
        else:
            main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        if CTK_AVAILABLE:
            title_label = ctk.CTkLabel(main_frame, text="配置向导",
                                     font=ctk.CTkFont(size=20, weight="bold"))
        else:
            title_label = ttk.Label(main_frame, text="配置向导",
                                  font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # API服务商选择
        if CTK_AVAILABLE:
            api_frame = ctk.CTkFrame(main_frame)
        else:
            api_frame = ttk.LabelFrame(main_frame, text="API服务商")
        api_frame.pack(fill="x", pady=(0, 15))

        if CTK_AVAILABLE:
            ctk.CTkLabel(api_frame, text="选择API服务商:",
                        font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        else:
            ttk.Label(api_frame, text="选择API服务商:",
                     font=("Arial", 10, "bold")).pack(anchor="w", padx=10, pady=(10, 5))

        # API选项
        self.api_var = tk.StringVar(value="openai")
        api_options = [
            ("OpenAI官方", "openai"),
            ("DeepSeek", "deepseek"),
            ("智谱AI", "zhipu"),
            ("月之暗面", "moonshot"),
            ("自定义", "custom")
        ]

        for text, value in api_options:
            if CTK_AVAILABLE:
                ctk.CTkRadioButton(api_frame, text=text, variable=self.api_var,
                                  value=value, command=self.on_api_change).pack(anchor="w", padx=20, pady=2)
            else:
                ttk.Radiobutton(api_frame, text=text, variable=self.api_var,
                               value=value, command=self.on_api_change).pack(anchor="w", padx=20, pady=2)

        # API配置
        if CTK_AVAILABLE:
            config_frame = ctk.CTkFrame(main_frame)
        else:
            config_frame = ttk.LabelFrame(main_frame, text="API配置")
        config_frame.pack(fill="x", pady=(0, 15))

        # API URL
        if CTK_AVAILABLE:
            ctk.CTkLabel(config_frame, text="API基础URL:").pack(anchor="w", padx=10, pady=(10, 0))
            self.api_url_var = tk.StringVar(value=self.config.API_BASE_URL)
            self.api_url_entry = ctk.CTkEntry(config_frame, textvariable=self.api_url_var)
            self.api_url_entry.pack(fill="x", padx=10, pady=(5, 10))
        else:
            ttk.Label(config_frame, text="API基础URL:").pack(anchor="w", padx=10, pady=(10, 0))
            self.api_url_var = tk.StringVar(value=self.config.API_BASE_URL)
            self.api_url_entry = ttk.Entry(config_frame, textvariable=self.api_url_var)
            self.api_url_entry.pack(fill="x", padx=10, pady=(5, 10))

        # API密钥
        if CTK_AVAILABLE:
            ctk.CTkLabel(config_frame, text="API密钥:").pack(anchor="w", padx=10, pady=(0, 0))
            self.api_key_var = tk.StringVar(value=self.config.API_KEY)
            self.api_key_entry = ctk.CTkEntry(config_frame, textvariable=self.api_key_var, show="*")
            self.api_key_entry.pack(fill="x", padx=10, pady=(5, 10))
        else:
            ttk.Label(config_frame, text="API密钥:").pack(anchor="w", padx=10, pady=(0, 0))
            self.api_key_var = tk.StringVar(value=self.config.API_KEY)
            self.api_key_entry = ttk.Entry(config_frame, textvariable=self.api_key_var, show="*")
            self.api_key_entry.pack(fill="x", padx=10, pady=(5, 10))

        # 模型名称
        if CTK_AVAILABLE:
            ctk.CTkLabel(config_frame, text="模型名称:").pack(anchor="w", padx=10, pady=(0, 0))
            self.model_var = tk.StringVar(value=self.config.MODEL_NAME)
            self.model_entry = ctk.CTkEntry(config_frame, textvariable=self.model_var)
            self.model_entry.pack(fill="x", padx=10, pady=(5, 10))
        else:
            ttk.Label(config_frame, text="模型名称:").pack(anchor="w", padx=10, pady=(0, 0))
            self.model_var = tk.StringVar(value=self.config.MODEL_NAME)
            self.model_entry = ttk.Entry(config_frame, textvariable=self.model_var)
            self.model_entry.pack(fill="x", padx=10, pady=(5, 10))

        # 按钮
        if CTK_AVAILABLE:
            button_frame = ctk.CTkFrame(main_frame)
        else:
            button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))

        if CTK_AVAILABLE:
            ctk.CTkButton(button_frame, text="测试连接", command=self.test_connection).pack(side="left", padx=(0, 10))
            ctk.CTkButton(button_frame, text="保存配置", command=self.save_config).pack(side="left", padx=(0, 10))
            ctk.CTkButton(button_frame, text="取消", command=self.window.destroy).pack(side="right")
        else:
            ttk.Button(button_frame, text="测试连接", command=self.test_connection).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side="left", padx=(0, 10))
            ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side="right")

        # 初始化API设置
        self.on_api_change()

    def on_api_change(self):
        """API服务商改变时的处理"""
        api_type = self.api_var.get()

        if api_type == "openai":
            self.api_url_var.set("https://api.openai.com/v1")
            self.model_var.set("gpt-3.5-turbo")
        elif api_type == "deepseek":
            self.api_url_var.set("https://api.deepseek.com/v1")
            self.model_var.set("deepseek-chat")
        elif api_type == "zhipu":
            self.api_url_var.set("https://open.bigmodel.cn/api/paas/v4")
            self.model_var.set("glm-4")
        elif api_type == "moonshot":
            self.api_url_var.set("https://api.moonshot.cn/v1")
            self.model_var.set("moonshot-v1-8k")

    def test_connection(self):
        """测试API连接"""
        # 更新配置
        self.config.API_BASE_URL = self.api_url_var.get()
        self.config.API_KEY = self.api_key_var.get()
        self.config.MODEL_NAME = self.model_var.get()

        try:
            from llm_service.openai_client import OpenAICompatibleClient
            client = OpenAICompatibleClient(self.config)

            if client.test_connection():
                messagebox.showinfo("测试成功", "✅ API连接测试成功！")
            else:
                messagebox.showerror("测试失败", "❌ API连接测试失败，请检查配置")
        except Exception as e:
            messagebox.showerror("测试错误", f"连接测试异常: {str(e)}")

    def save_config(self):
        """保存配置"""
        # 验证必填项
        if not self.api_key_var.get().strip():
            messagebox.showerror("配置错误", "API密钥不能为空")
            return

        # 更新配置
        self.config.API_BASE_URL = self.api_url_var.get()
        self.config.API_KEY = self.api_key_var.get()
        self.config.MODEL_NAME = self.model_var.get()

        try:
            # 保存到文件
            self.config.save_to_json_file("config.json")

            # 回调通知主窗口
            self.callback(self.config)

            # 关闭窗口
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("保存失败", f"保存配置失败: {str(e)}")

class GUILogHandler(logging.Handler):
    """自定义日志处理器，将日志发送到GUI队列"""

    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        """发送日志记录到队列"""
        try:
            msg = self.format(record)
            self.log_queue.put(msg)
        except Exception:
            self.handleError(record)

def main():
    """主函数"""
    app = DocumentToQuizGUI()
    app.run()

if __name__ == "__main__":
    main()
